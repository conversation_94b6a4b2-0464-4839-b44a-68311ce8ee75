{"cmake.sourceDirectory": "/home/<USER>/xiaoming/surgsmart_app/surgsmart/linux", "cSpell.words": ["alsa", "autoptr", "avaliable", "bluez", "bodycavity", "Bodyout", "bodyoutmask", "brightnessctl", "Cupertino", "dartfmt", "datas", "dbus", "easyloading", "evtest", "fial", "fullscreen", "g<PERSON>lean", "gchar", "getwidget", "gpointer", "gsettings", "kbps", "klass", "lldb", "l<PERSON>yin", "LTWH", "malloc", "mbps", "mediasfu", "mediasoup", "mousemove", "Mqtt", "mwcap", "networkmanager", "nmcli", "OLDBUILD", "OLDVERSION", "pactl", "pkla", "polkit", "prefs", "presign", "pubspec", "qrcode", "roboto", "rtmp", "ruishu", "<PERSON><PERSON><PERSON>", "screenutil", "scrollbars", "Socketio", "startai", "stopai", "strdupv", "str<PERSON>v", "struct", "sublist", "surg<PERSON><PERSON>", "surgsmart", "systime", "tdengine", "Typer", "unfocus", "unfreezed", "unmuted", "unsynchronized", "upgrader", "USERPROFILE", "webp", "wechat", "withai", "xdisplay", "x<PERSON><PERSON>", "x<PERSON>r"]}