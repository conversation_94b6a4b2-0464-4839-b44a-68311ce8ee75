{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "surgsmart",
      "request": "launch",
      "type": "dart",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/../medias_kit/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/../medias_kit/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "surgsmart (profile mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/../medias_kit/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/../medias_kit/linux/dependencies/xfyun_sdk/libs"
      }
    },
    {
      "name": "surgsmart (release mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "env": {
        "DISPLAY": ":0",
        "XF_ASSETS": "${workspaceFolder}/../medias_kit/assets",
        "LD_LIBRARY_PATH": "${workspaceFolder}/../medias_kit/linux/dependencies/xfyun_sdk/libs"
      }
    }
  ]
}
