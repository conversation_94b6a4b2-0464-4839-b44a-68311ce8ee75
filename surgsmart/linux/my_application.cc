#include "my_application.h"

#include <X11/Xlib.h>
#include <cstdio>
#include <flutter_linux/flutter_linux.h>
#ifdef GDK_WINDOWING_X11
#include <gdk/gdkx.h>
#endif

#include "desktop_multi_window/desktop_multi_window_plugin.h"

#include "flutter/generated_plugin_registrant.h"
#include "medias_kit/medias_kit_plugin.h"
#include <screen_retriever_linux/screen_retriever_linux_plugin.h>
#include <window_manager/window_manager_plugin.h>

static gboolean
on_window_state_event(GtkWidget *widget, GdkEventWindowState *event, gpointer data) {
    /// 检查窗口状态事件，确保窗口一直保持全屏,
    /// 如果尝试退出全屏，则重新设置为全屏
    gtk_window_set_decorated(GTK_WINDOW(widget), FALSE);
    gtk_window_set_default_size(GTK_WINDOW(widget), 3840, 1100);
    gtk_window_fullscreen(GTK_WINDOW(widget));
    // auto app = gtk_window_get_application(GTK_WINDOW(widget));
    // gtk_application_set_menubar(app, NULL);
    return FALSE;
}

struct _MyApplication {
    GtkApplication parent_instance;
    char **dart_entrypoint_arguments;
};

G_DEFINE_TYPE(MyApplication, my_application, GTK_TYPE_APPLICATION)

// Implements GApplication::activate.
static void
my_application_activate(GApplication *application) {
    MyApplication *self = MY_APPLICATION(application);
    GtkWindow *window =
        GTK_WINDOW(gtk_application_window_new(GTK_APPLICATION(application)));

    // Use a header bar when running in GNOME as this is the common style used
    // by applications and is the setup most users will be using (e.g. Ubuntu
    // desktop).
    // If running on X and not using GNOME then just use a traditional title bar
    // in case the window manager does more exotic layout, e.g. tiling.
    // If running on Wayland assume the header bar will work (may need changing
    // if future cases occur).
    gboolean use_header_bar = TRUE;
#ifdef GDK_WINDOWING_X11
    GdkScreen *screen = gtk_window_get_screen(window);
    if (GDK_IS_X11_SCREEN(screen)) {
        const gchar *wm_name = gdk_x11_screen_get_window_manager_name(screen);
        if (g_strcmp0(wm_name, "GNOME Shell") != 0) {
            use_header_bar = FALSE;
        }
    }
#endif
    if (use_header_bar) {
        GtkHeaderBar *header_bar = GTK_HEADER_BAR(gtk_header_bar_new());
        gtk_widget_show(GTK_WIDGET(header_bar));
        gtk_header_bar_set_title(header_bar, "surgsmart");
        gtk_header_bar_set_show_close_button(header_bar, TRUE);
        gtk_window_set_titlebar(window, GTK_WIDGET(header_bar));
    } else {
        gtk_window_set_title(window, "surgsmart");
    }

    // 修改初始化窗口属性, 并强制主屏幕全屏
    auto widget = GTK_WIDGET(window);
    gtk_window_set_default_size(window, 3840, 1100);
    // 设置背景色
    auto provider = gtk_css_provider_new();
    gtk_css_provider_load_from_data(
        provider, "window { background-color: #000000; }", -1, NULL
    );
    gtk_style_context_add_provider(gtk_widget_get_style_context(widget), GTK_STYLE_PROVIDER(provider), GTK_STYLE_PROVIDER_PRIORITY_APPLICATION);
    gtk_widget_show(widget);
    gtk_widget_set_can_focus(widget, true);
    auto display = gdk_display_get_default();
    auto monitorNum = gdk_display_get_n_monitors(display);
    for (auto i = 0; i < monitorNum; i++) {
        auto monitor = gdk_display_get_monitor(display, i);
        GdkRectangle geometry;
        gdk_monitor_get_geometry(monitor, &geometry);
        // 匹配主屏幕
        if (geometry.width == 3840 && geometry.height == 1100) {
            // 全屏
            gtk_window_fullscreen_on_monitor(window, screen, 0);
            break;
        }
    }
    // 保持屏幕唤醒状态
    gdk_x11_set_sm_client_id("surgsmart");
    gdk_x11_display_get_xdisplay(gdk_display_get_default());
    XSetScreenSaver(gdk_x11_get_default_xdisplay(), 0, 0, 0, 0);

    // 监听窗口变化
    g_signal_connect(
        window, "window-state-event", G_CALLBACK(on_window_state_event), NULL
    );

    g_autoptr(FlDartProject) project = fl_dart_project_new();
    fl_dart_project_set_dart_entrypoint_arguments(
        project, self->dart_entrypoint_arguments
    );

    FlView *view = fl_view_new(project);
    gtk_widget_show(GTK_WIDGET(view));
    gtk_container_add(GTK_CONTAINER(window), GTK_WIDGET(view));

    fl_register_plugins(FL_PLUGIN_REGISTRY(view));

    desktop_multi_window_plugin_set_window_created_callback([](FlPluginRegistry *registry) {
        g_autoptr(FlPluginRegistrar) window_manager_registrar =
            fl_plugin_registry_get_registrar_for_plugin(registry, "WindowManagerPlugin");
        window_manager_plugin_register_with_registrar(window_manager_registrar);
        g_autoptr(FlPluginRegistrar) screen_retriever_registrar =
            fl_plugin_registry_get_registrar_for_plugin(registry, "ScreenRetrieverLinuxPlugin");
        screen_retriever_linux_plugin_register_with_registrar(screen_retriever_registrar);

        g_autoptr(FlPluginRegistrar) mediasKitPlugin =
            fl_plugin_registry_get_registrar_for_plugin(registry, "MediasKitPlugin");
        medias_kit_plugin_register_with_registrar(mediasKitPlugin);
    });

    gtk_widget_grab_focus(GTK_WIDGET(view));
    gtk_window_set_keep_above(window, true);
}

// Implements GApplication::local_command_line.
static gboolean
my_application_local_command_line(GApplication *application, gchar ***arguments, int *exit_status) {
    MyApplication *self = MY_APPLICATION(application);
    // Strip out the first argument as it is the binary name.
    self->dart_entrypoint_arguments = g_strdupv(*arguments + 1);

    g_autoptr(GError) error = nullptr;
    if (!g_application_register(application, nullptr, &error)) {
        g_warning("Failed to register: %s", error->message);
        *exit_status = 1;
        return TRUE;
    }

    g_application_activate(application);
    *exit_status = 0;

    return TRUE;
}

// Implements GApplication::startup.
static void
my_application_startup(GApplication *application) {
    // MyApplication* self = MY_APPLICATION(object);

    // Perform any actions required at application startup.

    G_APPLICATION_CLASS(my_application_parent_class)->startup(application);
}

// Implements GApplication::shutdown.
static void
my_application_shutdown(GApplication *application) {
    // MyApplication* self = MY_APPLICATION(object);

    // Perform any actions required at application shutdown.

    G_APPLICATION_CLASS(my_application_parent_class)->shutdown(application);
}

// Implements GObject::dispose.
static void
my_application_dispose(GObject *object) {
    MyApplication *self = MY_APPLICATION(object);
    g_clear_pointer(&self->dart_entrypoint_arguments, g_strfreev);
    G_OBJECT_CLASS(my_application_parent_class)->dispose(object);
}

static void
my_application_class_init(MyApplicationClass *klass) {
    G_APPLICATION_CLASS(klass)->activate = my_application_activate;
    G_APPLICATION_CLASS(klass)->local_command_line =
        my_application_local_command_line;
    G_APPLICATION_CLASS(klass)->startup = my_application_startup;
    G_APPLICATION_CLASS(klass)->shutdown = my_application_shutdown;
    G_OBJECT_CLASS(klass)->dispose = my_application_dispose;
}

static void
my_application_init(MyApplication *self) {}

MyApplication *
my_application_new() {
    return MY_APPLICATION(g_object_new(my_application_get_type(), "application-id", APPLICATION_ID, "flags", G_APPLICATION_NON_UNIQUE, nullptr));
}
