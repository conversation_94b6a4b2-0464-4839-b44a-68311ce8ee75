#!/usr/bin/env bash

if [[ "${1}" == "--build" ]]; then
    shift
    dart run build_runner clean
    dart run build_runner build
fi

export FLUTTER_ENGINE_LOGGING=true

export DISPLAY=:0

XF_ASSETS=$(cd ../ && pwd)/medias_kit/assets
export XF_ASSETS

LD_LIBRARY_PATH=$(cd ../ && pwd)/medias_kit/linux/dependencies/xfyun_sdk/libs
export LD_LIBRARY_PATH

# if ! which xdotool >/dev/null 2>&1; then
#     sudo apt install -y xdotool || exit
# fi

# xdotool mousemove 1920 550 click 1

if [[ "${1}" == "--debug" ]]; then
    flutter build linux --debug || exit
    lldb ./build/linux/x64/debug/bundle/surgsmart
elif [[ "${1}" == "--release" ]]; then
    flutter run -d linux --dart-define=mode=development --release
elif [[ "${1}" == "--verbose" ]]; then
    flutter run -d linux --verbose
else
    flutter run -d linux --dart-define=mode=development #| grep "============>>"
fi
