import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:dart_vlc/dart_vlc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:screen_retriever/screen_retriever.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/db_utils.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/device_log.dart';
import 'package:surgsmart/src/widgets/app_launch_screen.widget.dart';
import 'package:window_manager/window_manager.dart';

/// 请先移步至 ./src/routes/go_paths.dart 中, 定义路由信息
import './src/routes/go_routes.dart';

/// APP 页面路由器
final router = GoRouter(
  observers: [AppRouteObserver.share],
  navigatorKey: app.navigatorKey,
  routes: routes,
  initialLocation:
      AppContext.enablePrivateServer
          ? (AppPreferences.privateServerUrl.stringValue?.isNotEmpty == true &&
                  AppPreferences.authorizeInfo.stringValue?.isNotEmpty == true)
              ? GoPaths.home
              : GoPaths.serviceSetting
          : GoPaths.home,
  //initialLocation: GoPaths.serviceSetting,
);

/// 多窗口路由
final expandRouter = GoRouter(
  observers: [AppRouteObserver.share],
  navigatorKey: app.navigatorKey,
  routes: routes,
  initialLocation: GoPaths.expandScreen,
);

class JsonLogPrinter extends LogPrinter {
  final bool recordStackTrace;
  JsonLogPrinter({this.recordStackTrace = true});
  @override
  List<String> log(LogEvent event) {
    final map = {
      "timestamp": event.time.dateFormat("YYYY-MM-DD hh:mm:ss.SSS"),
      "level": event.level.name,
      "content": event.message,
    };
    if (recordStackTrace) {
      map["stackTrace"] = StackTrace.current
          .toString()
          .split('\n')[3]
          .replaceAll(RegExp(r'^#\d+\s+'), "");
    }
    return [jsonEncode(map)];
  }
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

@AppDetector()
void main(List<String> args) async {
  HttpOverrides.global = MyHttpOverrides();

  Size? size;
  bool multiWindow = args.firstOrNull == 'multi_window';
  AppContext.multiWindow = multiWindow;
  if (multiWindow) {
    await initMultiWindow(args);
    //大屏设计图大小
    size = const Size(1920, 1080);
  }
  runApp(
    ScreenUtilInit(
      designSize: size ?? const Size(3840, 1100),
      minTextAdapt: true,
      builder: (context, child) {
        return AppBuilder(
          launchScreen: const AppLaunchScreen(),
          builder: (context, env) {
            return MaterialApp.router(
              debugShowCheckedModeBanner: false,
              scaffoldMessengerKey: app.messengerKey,
              routerConfig: multiWindow ? expandRouter : router,
              themeMode: ThemeMode.dark,
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: S.delegate.supportedLocales,
              localeResolutionCallback: (locale, supportedLocales) {
                return const Locale('zh', 'CN');
                // if (locale?.countryCode == 'CN') {
                //   return const Locale('zh', 'CN');
                // } else {
                //   return const Locale('en', 'US');
                // }
              },
              darkTheme: ThemeData.dark(useMaterial3: true).copyWith(
                appBarTheme: ThemeData.dark(useMaterial3: true).appBarTheme
                    .copyWith(titleTextStyle: TextStyle(fontSize: 56.sp)),
                textTheme: ThemeData.dark(
                  useMaterial3: true,
                ).textTheme.copyWith(bodyMedium: TextStyle(fontSize: 100.sp)),
              ),
              theme: ThemeData.light(useMaterial3: true),
            );
          },
          onWillInit: () async {
            //初始化顺序需注意
            /// 偏好设置
            await AppPreferences.init();

            /// 运行环境
            await AppContext.share.init();

            /// 数据库初始化
            await DbUtils.instance.init();

            /// 日志初始化
            setLogConfig();

            /// 多媒体插件初始化
            await MediasKit.ensureInitialized();
            HostDevice.share.startListenVideoCaptureDevices();
            HostDevice.share.startListenAudioDevices();

            if (multiWindow) return;

            /// 播放器插件初始化
            DartVLC.initialize();

            /// 避免app崩溃等原因导致电源按键失效无法关闭，启动恢复电源按键行为
            await DeviceCmd.share.disablePowerButton(disable: false);

            /// 默认关闭呼吸灯
            try {
              Light.share.active(LightAction.off);
            } catch (e) {
              app.logE("呼吸灯控制失败: $e");
            }

            /// 等待启动动画
            await Future.delayed(6.seconds);

            /// 环境变量向下传递
          },
          onDidInit: (env) {
            app.logI("App did init");
          },
        );
      },
    ),
  );
}

void setLogConfig() {
  if (kDebugMode) {
    app.setLog(Level.warning);
    app.setDeviceLog(Level.error);
  } else {
    app.setLog(
      Level.info,
      printer: JsonLogPrinter(),
      output: AdvancedFileOutput(
        path: "${AppContext.share.documentsPath}/v202310/surgsmart.logs",
        maxFileSizeKB: 10 * 1024,
        maxRotatedFilesCount: 7,
        latestFileName: "surgsmart.log",
        fileNameFormatter: (timestamp) {
          return "surgsmart-${timestamp.millisecondsSinceEpoch ~/ 1000}.log";
        },
      ),
    );
    app.setDeviceLog(
      Level.info,
      printer: JsonLogPrinter(recordStackTrace: false),
      output: AdvancedFileOutput(
        path: "${AppContext.share.documentsPath}/v202310/surgsmart.logs",
        maxFileSizeKB: 10 * 1024,
        maxRotatedFilesCount: 7,
        latestFileName: "surgsmart.device.log",
        fileNameFormatter: (timestamp) {
          return "surgsmart-${timestamp.millisecondsSinceEpoch ~/ 1000}.device.log";
        },
      ),
    );
  }
}

Future<void> initMultiWindow(List<String> args) async {
  // final windowId = int.parse(args[1]);
  final argument =
      args[2].isEmpty ? const {} : jsonDecode(args[2]) as Map<String, dynamic>;

  Map<String, dynamic>? display =
      argument['targetDisplay'] as Map<String, dynamic>?;
  if (display == null) return;
  Display targetScreen = Display.fromJson(display);

  WidgetsFlutterBinding.ensureInitialized();
  await windowManager.ensureInitialized();

  // 获取目标屏幕的大小
  Size size = Size(targetScreen.size.width, targetScreen.size.height);

  // 设置全屏窗口
  WindowOptions windowOptions = WindowOptions(
    size: size,
    titleBarStyle: TitleBarStyle.hidden,
    backgroundColor: Colors.transparent,
    windowButtonVisibility: false,
  );

  // 等待窗口准备就绪后调整位置
  Offset targetOffset = Offset(targetScreen.visiblePosition?.dx ?? 3840, 0);

  windowManager.waitUntilReadyToShow(windowOptions, () async {
    // 将窗口移动到目标屏幕的左上角
    await windowManager.setPosition(targetOffset);

    await windowManager.show();
    await windowManager.focus();
  });
}
