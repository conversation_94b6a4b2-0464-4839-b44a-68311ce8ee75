import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/widgets/keyboard.widget.dart';

enum KeypadType { numeric, decimal, fullKey }

class KeypadModal extends StatefulWidget {
  final String? text;
  final String? placeholder;
  final double? modalHeight;
  final double? modalWidth;
  final KeypadType? keypadType;
  final EdgeInsets? modalPadding;

  final bool inputHideControl;

  final void Function(String text) onConfirm;

  const KeypadModal({
    super.key,
    this.placeholder = '请输入',
    this.keypadType = KeypadType.fullKey,
    this.inputHideControl = false,
    this.modalHeight,
    this.modalWidth,
    required this.onConfirm,
    this.text,
    this.modalPadding,
  });

  @override
  State<KeypadModal> createState() => _NumericKeypadModalState();

  void show(BuildContext context, {bool barrierDismissible = true}) {
    showDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (context) => Center(child: this),
    );
  }
}

class _NumericKeypadModalState extends State<KeypadModal> {
  String? text;
  late double _modalHeight;
  late double _modalWidth;
  late EdgeInsets _modalPadding;
  bool hideInput = false;

  @override
  void initState() {
    text = widget.text;
    hideInput = widget.inputHideControl ? true : false;
    _modalHeight = widget.modalHeight ?? 940.h;
    _modalWidth = widget.modalWidth ??
        (widget.keypadType == KeypadType.fullKey ? 2168.w : 1755.w);
    _modalPadding = widget.modalPadding ??
        (widget.keypadType == KeypadType.fullKey
            ? EdgeInsets.symmetric(horizontal: 322.w)
            : EdgeInsets.symmetric(horizontal: 389.w));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Material(
          borderRadius: BorderRadius.circular(16.r),
          child: Container(
            width: _modalWidth,
            height: _modalHeight,
            padding: _modalPadding,
            decoration: BoxDecoration(
              border: Border.all(
                width: 4.w,
                color: const Color(0xFFFFFFFF).withValues(alpha: 0.48),
              ),
              borderRadius: BorderRadius.circular(30.w),
            ),
            child: DefaultTextStyle.merge(
              style: TextStyle(
                color: Colors.white,
                fontSize: 72.sp,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  40.verticalSpace,
                  Container(
                    alignment: Alignment.center,
                    width: double.infinity,
                    height: 196.h,
                    decoration: BoxDecoration(
                      color: const Color(0xff121820),
                      border: Border.all(
                        width: 8.w,
                        color: const Color(0xff0EC6D2),
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(24.w)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            getTextDisplay() ?? widget.placeholder ?? '',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 72.sp,
                              color: text != null
                                  ? Colors.white
                                  : Colors.white.withValues(alpha: 0.6),
                            ),
                          ),
                        ),
                        if (widget.inputHideControl)
                          Button(
                            padding: EdgeInsets.only(right: 60.w),
                            icon: Icon(
                              hideInput
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              size: 82,
                              color: Colors.white,
                            ),
                            onPressed: () {
                              setState(() {
                                hideInput = !hideInput;
                              });
                            },
                          ),
                      ],
                    ),
                  ),
                  65.verticalSpace,
                  Expanded(
                    child: widget.keypadType == KeypadType.fullKey
                        ? KeyboardInput(
                            onKeyTap: (key) {},
                            onLongKeyTap: (key) {
                              if (key.type == KeyType.backspace) {
                                setState(() {
                                  text = null;
                                });
                              }
                            },
                            onBackspace: () {
                              if (text?.isNotEmpty == true) {
                                text = text!.substring(0, text!.length - 1);
                                if (text!.isEmpty) {
                                  text = null;
                                }
                              }
                              setState(() {});
                            },
                            onChar: (char) {
                              text ??= '';
                              setState(() {
                                text = '$text$char';
                              });
                            },
                            onEnter: () {
                              widget.onConfirm(text ?? '');
                            },
                          )
                        : NumericKeypad(
                            isDecimal: widget.keypadType == KeypadType.decimal,
                            onConfirm: () {
                              widget.onConfirm(text ?? '');
                            },
                            onBackspace: () {
                              if (text?.isNotEmpty == true) {
                                text = text!.substring(0, text!.length - 1);
                                if (text!.isEmpty) {
                                  text = null;
                                }
                              }
                              setState(() {});
                            },
                            onChar: (String value) {
                              text ??= '';
                              if (text!.length < 20) {
                                setState(() {
                                  text = '$text$value';
                                });
                              }
                            },
                            onClear: () {
                              setState(() {
                                text = null;
                              });
                            },
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: 73.h,
          right: 77.w,
          height: 80.r,
          width: 80.r,
          child: Button(
            borderRadius: BorderRadius.circular(40.r),
            borderSide: BorderSide(color: Colors.white, width: 4.w),
            onPressed: () {
              Navigator.of(context).pop();
            },
            icon: Icon(
              Icons.close,
              size: 64.r,
            ),
          ),
        )
      ],
    );
  }

  String? getTextDisplay() {
    if (text?.isNotEmpty == true && hideInput) {
      return text!.replaceAll(RegExp(r'\S'), '*');
    }
    return text;
  }
}
