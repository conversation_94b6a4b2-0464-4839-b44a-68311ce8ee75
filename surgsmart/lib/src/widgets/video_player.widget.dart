import 'dart:io';

import 'package:dart_vlc/dart_vlc.dart';
import 'package:flutter/material.dart';

/// 视频播放组件，支持本地和网络视频播放
class VideoWidget extends StatefulWidget {
  final void Function(Player player)? playListener;

  final List<String> path;
  final BoxFit fit;
  final double scale;
  final bool showControls;
  final bool loop;
  final double rate;

  /// 构造参数说明：
  /// - [path] 视频文件路径，支持本地和网络视频
  /// - [fit] 视频填充方式，默认为`BoxFit.contain`
  /// - [scale] 视频缩放比例，默认为1.0
  /// - [showControls] 是否显示控制栏，默认为`false`
  /// - [loop] 是否循环播放，默认为`false`
  /// - [rate] 视频播放速度，默认为1.0
  const VideoWidget({
    required this.path,
    this.fit = BoxFit.contain,
    this.scale = 1.0,
    this.rate = 1.0,
    this.showControls = false,
    this.loop = false,
    this.playListener,
    super.key,
  });
  @override
  State<VideoWidget> createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {
  Player player = Player(
    id: 69420,
    //videoDimensions: const VideoDimensions(640, 360),
  );

  @override
  void initState() {
    super.initState();
    if (widget.path.isEmpty) {
      return;
    }
    player.setVolume(0);
    player.setRate(widget.rate);
    if (widget.loop) {
      player.setPlaylistMode(PlaylistMode.loop);
    }
    List<Media> medias =
        widget.path.map((path) {
          Uri uri = Uri.parse(path);
          return (uri.scheme == 'file' || uri.scheme.isEmpty)
              ? Media.file(File(uri.path))
              : Media.network(widget.path);
        }).toList();

    player.open(Playlist(medias: medias));
    widget.playListener?.call(player);

    // videoStreamControllers[player.id] =
    //     StreamController<VideoFrame>.broadcast();
    // videoStreamControllers[player.id]?.stream.listen((
    //   VideoFrame videoFrame,
    // ) async {
    //   app.logW('--------videoFrame:${videoFrame.byteArray.length}');
    // });
  }

  @override
  void dispose() {
    player.stop();
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Video(
      player: player,
      fit: widget.fit,
      scale: widget.scale,
      showControls: widget.showControls,
      progressBarThumbRadius: 20,
      progressBarThumbGlowRadius: 100,
      progressBarTextStyle: TextStyle(fontSize: 40),
    );
  }
}
