import 'rtc_signal.model.dart';
import 'rtc_signal.dart';

/// RTC信令观察者接口
/// 用于监听RTC信令相关的事件
abstract interface class RtcSignalObserver {
  /// RTC信令异常回调
  /// [message] 异常信息
  void rtcSignalException(dynamic message);

  /// RTC信令状态变更回调
  /// [status] 当前状态
  void rtcSignalStatusChanged(RtcSignalStatus status);

  /// RTC对等端加入回调
  /// [peer] 加入的对等端信息
  void rtcSignalPeerJoined(RtcPeer peer);

  /// RTC对等端信息更新回调
  /// [peer] 更新后的对等端信息
  void rtcSignalPeerUpdated(RtcPeer peer);

  /// RTC对等端离开回调
  /// [peer] 离开的对等端信息
  void rtcSignalPeerLeft(RtcPeer peer);

  /// RTC新的消费者流回调
  /// [streamOption] 流配置选项
  void rtcSignalNewConsumer(RtcStreamOption streamOption);
}
