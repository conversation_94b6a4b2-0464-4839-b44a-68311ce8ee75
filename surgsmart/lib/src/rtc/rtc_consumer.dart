import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;

import 'rtc_signal.dart';

/// RTC消费者类，用于处理媒体流的消费
/// 实现了RtcMediaHandler接口
class RtcConsumer implements RtcMediaHandler {
  /// mediasoup消费者实例
  final msp.Consumer _consumer;

  /// RTC信令实例，用于与服务器通信
  final RtcSignal _signal;

  /// RTC事件处理器的弱引用，避免循环引用
  final WeakReference<RtcEventHandler> _rtcEventHandlerRef;

  /// 构造函数
  /// @param consumer mediasoup消费者实例
  /// @param signal RTC信令实例
  /// @param rtcEventHandlerRef RTC事件处理器的弱引用
  RtcConsumer(this._consumer, this._signal, this._rtcEventHandlerRef);

  @override
  msp.MediaStreamTrack get track => _consumer.track;

  @override
  Future<void> resume() async {
    await _signal.resumeConsumer(_consumer.id);
    _consumer.resume();
  }

  @override
  Future<void> pause() async {
    await _signal.pauseConsumer(_consumer.id);
    _consumer.pause();
  }

  @override
  Future<void> close() async {
    await _consumer.close();
    _rtcEventHandlerRef.target?.clearSource(_consumer.id);
  }
}
