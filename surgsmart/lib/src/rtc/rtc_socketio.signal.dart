import 'dart:convert';

import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;
import 'package:socket_io_client/socket_io_client.dart';

import 'rtc_log.observer.dart';
import 'rtc_signal.model.dart';
import 'rtc_signal.dart';
import 'rtc_signal.observer.dart';

/// RTC Socket.IO 信号观察者接口
abstract interface class RtcSocketioSignalObserver
    implements RtcLogObserver, RtcSignalObserver {}

/// RTC Socket.IO 信号实现类
class RtcSocketioSignal implements RtcSignal {
  /// Socket.IO 服务器URL
  final String _url;

  /// 认证令牌
  final String _authToken;

  /// 信号观察者的弱引用
  final WeakReference<RtcSocketioSignalObserver> _observer;

  /// Socket.IO 客户端实例
  late final Socket _socket;

  /// 房间ID
  late final String _roomId;

  /// 当前用户的Peer信息
  late final RtcPeer _peerSelf;

  /// 信号连接状态
  var _status = RtcSignalStatus.connecting;

  /// 是否在房间中
  var _isInRoom = false;

  /// RTC事件处理器的弱引用
  WeakReference<RtcEventHandler>? _rtcEventHandlerRef;

  @override
  String get roomId => _roomId;

  @override
  RtcPeer get peerSelf => _peerSelf;

  @override
  RtcSignalStatus get status => _status;

  @override
  bool get isInRoom => _isInRoom;

  /// 构造函数
  RtcSocketioSignal(this._url, this._authToken, this._observer) {
    assert(_url.isNotEmpty, 'URL cannot be empty');
    _socket = io(
      "$_url/signal",
      OptionBuilder().setTransports(['websocket']).setAuth({
        "authorization": "Bearer $_authToken",
      }).build(),
    );
    _socket.onError((error) {
      _observer.target?.rtcSignalException(error);
    });
    _socket.onConnect((data) {
      _status = RtcSignalStatus.connected;
      _observer.target?.rtcSignalStatusChanged(_status);
    });
    _socket.onDisconnect((data) {
      _status = RtcSignalStatus.disconnected;
      _observer.target?.rtcSignalStatusChanged(_status);
    });
    _onEvents();
    final jsonStr = base64Url.normalize(_authToken.split(".")[1]);
    final map = jsonDecode(utf8.decode(base64Url.decode(jsonStr)));
    _roomId = map["ri"];
    _peerSelf = RtcPeer(
      map["pi"],
      RtcPeerRole.fromString(map['pr']),
      map["pn"],
      map["pa"],
    );
  }

  /// 释放资源
  @override
  void dispose() {
    _socket.dispose();
  }

  /// 锁定信号处理器
  @override
  void lock(WeakReference<RtcEventHandler> rtcEventHandlerRef) {
    if (_rtcEventHandlerRef != null) {
      throw Exception('Signal is locked by rtcEventHandler');
    } else {
      _rtcEventHandlerRef = rtcEventHandlerRef;
    }
  }

  /// 检查WebSocket响应状态
  void _checkResponse(WsResMessage res) {
    if (res.status == WsResStatus.err) {
      throw Exception(res.reason);
    }
  }

  /// 加入房间
  @override
  Future<void> joinRoom() async {
    final map = await _socket.emitWithAckAsync(
      'joinRoom',
      WsMessage.only().toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
    _isInRoom = true;
  }

  /// 离开房间
  @override
  Future<void> leaveRoom() async {
    final map = await _socket.emitWithAckAsync(
      'leaveRoom',
      WsMessage.only().toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
    _isInRoom = false;
  }

  /// 获取路由器RTP能力
  @override
  Future<msp.RtpCapabilities> getRouterRtpCapabilities() async {
    final map = await _socket.emitWithAckAsync(
      'getRouterRtpCapabilities',
      WsMessage.only().toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
    return msp.RtpCapabilities.fromMap(res.payload!);
  }

  /// 创建WebRTC传输
  @override
  Future<Map<String, dynamic>> createWebRtcTransport(
    bool enableProduce,
    bool enableConsume,
  ) async {
    final map = await _socket.emitWithAckAsync(
      'createWebRtcTransport',
      WsMessage.only(
        payload: {
          "enableProduce": enableProduce,
          "enableConsume": enableConsume,
        },
      ).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
    return res.payload!;
  }

  /// 连接WebRTC传输
  @override
  Future<void> connectWebRtcTransport(
    String transportId,
    msp.DtlsParameters dtlsParameters,
  ) async {
    final map = await _socket.emitWithAckAsync(
      'connectWebRtcTransport',
      WsMessage.only(
        payload: {
          "transportId": transportId,
          "dtlsParameters": dtlsParameters.toMap(),
        },
      ).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
  }

  /// 生产媒体流
  @override
  Future<String> produce(
    String transportId,
    MediaKind kind,
    msp.RtpParameters rtpParameters,
  ) async {
    final map = await _socket.emitWithAckAsync(
      'produce',
      WsMessage.only(
        payload: {
          "transportId": transportId,
          "kind": kind.name,
          "rtpParameters": rtpParameters.toMap(),
        },
      ).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
    return res.payload!["producerId"];
  }

  /// 关闭生产者
  @override
  Future<void> closeProducer(String id) async {
    final map = await _socket.emitWithAckAsync(
      'closeProducer',
      WsMessage.only(payload: {"producerId": id}).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
  }

  /// 暂停生产者
  @override
  Future<void> pauseProducer(String id) async {
    final map = await _socket.emitWithAckAsync(
      'pauseProducer',
      WsMessage.only(payload: {"producerId": id}).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
  }

  /// 恢复生产者
  @override
  Future<void> resumeProducer(String id) async {
    final map = await _socket.emitWithAckAsync(
      'resumeProducer',
      WsMessage.only(payload: {"producerId": id}).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
  }

  /// 消费媒体流
  @override
  Future<void> consume(
    String transportId,
    msp.RtpCapabilities deviceRtpCapabilities,
  ) async {
    final map = await _socket.emitWithAckAsync(
      'consume',
      WsMessage.only(
        payload: {
          "transportId": transportId,
          "rtpCapabilities": deviceRtpCapabilities.toMap(),
        },
      ).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
  }

  /// 关闭消费者
  @override
  Future<void> closeConsumer(String id) async {
    throw UnimplementedError();
  }

  /// 暂停消费者
  @override
  Future<void> pauseConsumer(String id) async {
    final map = await _socket.emitWithAckAsync(
      'pauseConsumer',
      WsMessage.only(payload: {"consumerId": id}).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
  }

  /// 恢复消费者
  @override
  Future<void> resumeConsumer(String id) async {
    final map = await _socket.emitWithAckAsync(
      'resumeConsumer',
      WsMessage.only(payload: {"consumerId": id}).toMap(),
    );
    final res = WsResMessage.fromMap(map);
    _checkResponse(res);
  }

  /// 注册Socket.IO事件处理
  void _onEvents() {
    // 处理peer加入事件
    _socket.on('peerJoined', (data) {
      if (data is List) {
        final message = WsMessage.fromMap(data.first);
        _observer.target?.rtcSignalPeerJoined(
          RtcPeer.fromMap(message.payload!),
        );
        data.last(WsResMessage.success().toMap());
      }
    });

    // 处理peer更新事件
    _socket.on('peerUpdated', (data) {
      if (data is List) {
        final message = WsMessage.fromMap(data.first);
        _observer.target?.rtcSignalPeerUpdated(
          RtcPeer.fromMap(message.payload!),
        );
        data.last(WsResMessage.success().toMap());
      }
    });

    // 处理peer离开事件
    _socket.on('peerLeft', (data) {
      if (data is List) {
        final message = WsMessage.fromMap(data.first);
        _observer.target?.rtcSignalPeerLeft(RtcPeer.fromMap(message.payload!));
        data.last(WsResMessage.success().toMap());
      }
    });

    // 处理新消费者事件
    _socket.on('newConsumer', (data) {
      if (data is List) {
        final message = WsMessage.fromMap(data.first);
        _observer.target?.rtcSignalNewConsumer(
          RtcStreamOption.fromMap(message.payload!),
        );
        data.last(WsResMessage.success().toMap());
      }
    });
  }
}
