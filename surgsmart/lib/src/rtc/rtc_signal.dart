import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;

import 'rtc_signal.model.dart';

/// 媒体类型枚举
enum MediaKind {
  /// 音频类型
  audio,

  /// 视频类型
  video;

  /// 从字符串创建MediaKind枚举
  ///
  /// [str] 字符串表示的媒体类型
  /// 返回对应的MediaKind枚举值
  factory MediaKind.fromString(String str) {
    return values.firstWhere((e) => e.name == str);
  }
}

/// WebSocket消息类
class WsMessage {
  /// 消息创建时间戳
  final int cat;

  /// 消息过期时间戳
  final int exp;

  /// 消息负载数据
  final Map<String, dynamic>? payload;

  const WsMessage(this.cat, this.exp, [this.payload]);

  /// 创建一个只包含超时时间和负载的消息
  factory WsMessage.only({int timeout = 5000, Map<String, dynamic>? payload}) {
    final cat = DateTime.now().millisecondsSinceEpoch;
    final exp = cat + timeout;
    return WsMessage(cat, exp, payload);
  }

  /// 从Map创建消息对象
  factory WsMessage.fromMap(Map<String, dynamic> map) {
    return WsMessage(map['cat'], map['exp'], map['payload']);
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {"cat": cat, "exp": exp, "payload": payload};
  }

  @override
  String toString() {
    return toMap().toString();
  }

  /// 检查消息是否有效
  isValid() {
    return DateTime.now().millisecondsSinceEpoch > exp;
  }
}

/// WebSocket响应状态枚举
enum WsResStatus {
  ok,
  err;

  factory WsResStatus.fromString(String status) {
    return WsResStatus.values.firstWhere((e) => e.name == status.toLowerCase());
  }
}

/// WebSocket响应消息类
class WsResMessage {
  /// 消息创建时间戳
  final int cat;

  /// 响应状态
  final WsResStatus status;

  /// 响应原因
  final String reason;

  /// 响应负载数据
  final Map<String, dynamic>? payload;

  WsResMessage(this.cat, this.status, this.reason, [this.payload]);

  /// 从Map创建响应消息对象
  factory WsResMessage.fromMap(Map<String, dynamic> map) {
    return WsResMessage(
      map['cat'],
      WsResStatus.fromString(map['status']),
      map['reason'],
      map['payload'],
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      "cat": cat,
      "status": status.name,
      "reason": reason,
      "payload": payload,
    };
  }

  @override
  String toString() {
    return toMap().toString();
  }

  /// 创建成功响应消息
  factory WsResMessage.success([Map<String, dynamic>? payload]) {
    return WsResMessage(
      DateTime.now().millisecondsSinceEpoch,
      WsResStatus.ok,
      "request success",
      payload,
    );
  }

  /// 创建失败响应消息
  factory WsResMessage.failure(String reason) {
    return WsResMessage(
      DateTime.now().millisecondsSinceEpoch,
      WsResStatus.err,
      reason,
    );
  }
}

/// RTC信号状态枚举
enum RtcSignalStatus { connecting, connected, disconnected }

/// RTC媒体处理接口
abstract interface class RtcMediaHandler {
  /// 获取媒体轨道
  msp.MediaStreamTrack get track;

  /// 恢复媒体流
  Future<void> resume();

  /// 暂停媒体流
  Future<void> pause();

  /// 关闭媒体流
  Future<void> close();
}

/// RTC事件处理接口
abstract interface class RtcEventHandler {
  /// 清除指定来源
  void clearSource(String source);
}

/// RTC信号接口
abstract interface class RtcSignal {
  /// 获取房间ID
  String get roomId;

  /// 获取本地对等端
  RtcPeer get peerSelf;

  /// 获取信号状态
  RtcSignalStatus get status;

  /// 是否在房间中
  bool get isInRoom;

  /// 释放资源
  void dispose();

  /// 锁定事件处理器
  void lock(WeakReference<RtcEventHandler> rtcEventHandlerRef);

  /// 加入房间
  Future<void> joinRoom();

  /// 离开房间
  Future<void> leaveRoom();

  /// 获取路由器RTP能力
  Future<msp.RtpCapabilities> getRouterRtpCapabilities();

  /// 创建WebRTC传输
  Future<Map<String, dynamic>> createWebRtcTransport(
    bool enableProduce,
    bool enableConsume,
  );

  /// 连接WebRTC传输
  Future<void> connectWebRtcTransport(
    String transportId,
    msp.DtlsParameters dtlsParameters,
  );

  // closeWebRtcTransport();

  /// 生产媒体流
  Future<String> produce(
    String transportId,
    MediaKind kind,
    msp.RtpParameters rtpParameters,
  );

  /// 恢复生产者
  Future<void> resumeProducer(String id);

  /// 暂停生产者
  Future<void> pauseProducer(String id);

  /// 关闭生产者
  Future<void> closeProducer(String id);

  /// 消费媒体流
  Future<void> consume(
    String transportId,
    msp.RtpCapabilities deviceRtpCapabilities,
  );

  /// 恢复消费者
  Future<void> resumeConsumer(String id);

  /// 暂停消费者
  Future<void> pauseConsumer(String id);

  /// 关闭消费者
  Future<void> closeConsumer(String id);
}
