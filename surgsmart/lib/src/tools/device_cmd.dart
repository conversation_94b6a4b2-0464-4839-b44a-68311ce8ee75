import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/app_config.dart';
import 'package:surgsmart/src/apis/http/tdengine.api.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/models/network_device_model.dart';

/// 设备命令
class DeviceCmd {
  DeviceCmd._();

  static final share = DeviceCmd._();

  /// 管理需要控制的process
  final Map<String, int> processPids = {};

  /// 重启 APP
  void restartApp() async {
    await Process.run('bash', [
      '-c',
      'systemctl --user restart surgsmart-operating-room',
    ]);
  }

  /// 重启设备
  void restartDevice() async {
    await Process.run('sudo', ['/usr/sbin/shutdown', '-r', 'now']);
  }

  /// 关闭设备
  void shutdownDevice() async {
    await Process.run('sudo', ['/usr/sbin/shutdown', '-h', 'now']);
  }

  /// 调整屏幕亮度
  ///
  /// - [brightness] 亮度值，范围为 0 到 100，默认为 100
  void setBrightness([int brightness = 80]) async {
    assert(
      brightness >= 0 && brightness <= 100,
      'Brightness must be between 0 and 100',
    );
    await Process.run('sudo', ['brightnessctl', 'set', '$brightness']);
  }

  /// 获取屏幕亮度
  Future<int> getBrightness() async {
    final result = await Process.run('sudo', ['brightnessctl', 'get']);
    return int.tryParse(result.stdout.toString().trim()) ?? 0;
  }

  /// 获取video文件描述符
  Future<String> videoPathInfo() async {
    final result = await Process.run('mwcap-info', ['-v', '0:0']);
    return result.stdout.toString().trim();
  }

  /// 监听电源按键
  Future<Process> powerButton() async {
    final result = await Process.start('sudo', ['evtest', '/dev/input/event2']);
    return result;
  }

  /// 屏蔽电源按键动作
  Future<bool> disablePowerButton({bool disable = true}) async {
    final result = await Process.run('gsettings', [
      'set',
      'org.gnome.settings-daemon.plugins.power',
      'power-button-action',
      disable ? "'nothing'" : "'interactive'",
    ]);
    return result.exitCode == 0;
  }

  /// 屏蔽wifi连接失败密码输入框弹出
  Future<bool> disableWifiPasswordInputBox({bool disable = true}) async {
    var command =
        '''gsettings set org.gnome.nm-applet disable-connected-notifications $disable;
gsettings set org.gnome.nm-applet disable-disconnected-notifications $disable;
gsettings set org.gnome.nm-applet suppress-wireless-networks-available $disable''';
    var res = await Process.run('bash', ['-c', command]);
    return res.exitCode == 0;
  }

  /// 获取所有网络状态
  Future<String> getAllNetWorkStatus() async {
    final result = await Process.run('nmcli', ['device']);
    return result.stdout.toString();
  }

  /// 获取设备ip信息
  Future<String> getIpInfo() async {
    final result = await Process.run('ip', ['-j', 'addr']);
    return result.stdout.toString();
  }

  /// 设置网络优先级
  Future<bool> setNetWorkPriority(
    List<String> sequenceList, {
    bool auto = false,
  }) async {
    int maxMetric = sequenceList.length == 1 ? 95 : 99; //独立一个设备时优先级为最高,值越低优先级越高
    try {
      for (var connection in sequenceList.reversed) {
        await Process.run('sudo', [
          'nmcli',
          'connection',
          'modify',
          connection,
          'ipv4.route-metric',
          '$maxMetric',
        ]);

        await Process.run('sudo', [
          'nmcli',
          'connection',
          'modify',
          connection,
          'ipv6.route-metric',
          '$maxMetric',
        ]);
        //重启网络生效
        if (!await reConnectNetwork(connection)) {
          return false;
        }
        if (!auto) {
          maxMetric -= 1;
        }
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 重连网络
  Future<bool> reConnectNetwork(String connection) async {
    await connectionNetwork(connection, false);
    ProcessResult result = await connectionNetwork(connection, true);
    if (result.exitCode != 0) {
      app.logW("network set err：${result.stdout.toString()}");
    }
    return result.exitCode == 0;
  }

  /// 连接或断开指定网络
  Future<ProcessResult> connectionNetwork(
    String connection,
    bool isConnect,
  ) async {
    return Process.run('sudo', [
      'nmcli',
      'connection',
      isConnect ? 'up' : 'down',
      connection,
    ]);
  }

  /// 启用或禁用wifi网络
  Future<ProcessResult> setWifiEnable(bool enable) async {
    return Process.run('sudo', [
      'nmcli',
      'radio',
      'wifi',
      enable ? 'on' : 'off',
    ]);
  }

  /// wifi网络是否启用
  Future<bool> wifiAvailable() async {
    ProcessResult res = await Process.run('sudo', ['nmcli', 'radio', 'wifi']);
    return res.stdout.toString().contains('enabled');
  }

  /// 启用或禁用Mobile网络
  Future<bool> setMobileEnable(bool enable) async {
    ProcessResult res = await Process.run('sudo', [
      'nmcli',
      'radio',
      'wwan',
      enable ? 'on' : 'off',
    ]);
    return res.exitCode == 0;
  }

  /// mobile网络是否启用
  Future<bool> mobileAvailable() async {
    ProcessResult res = await Process.run('sudo', ['nmcli', 'radio', 'wwan']);
    return res.stdout.toString().contains('enabled');
  }

  /// 手动配置网络
  Future<bool> manualConfigConnection({
    required String connection,
    required String ip,
    required String netmask,
    required String gateway,
    required String dns,
  }) async {
    var command =
        "sudo nmcli con mod '$connection' ipv4.addresses '$ip/$netmask' ipv4.gateway '$gateway' ipv4.dns '$dns' ipv4.method manual";
    var res = await Process.run('bash', ['-c', command]);

    app.logW('${res.stdout}, ${res.stderr}');
    return res.exitCode == 0;
  }

  /// 清除手动配置的网络
  Future<bool> cleanManualConfig({required String connection}) async {
    var command =
        "sudo nmcli con mod '$connection' ipv4.addresses '' ipv4.gateway '' ipv4.dns ''";
    var res = await Process.run('bash', ['-c', command]);
    app.logW('cleanManualConfig：${res.stdout}, ${res.stderr}');
    return res.exitCode == 0;
  }

  /// 是否启用动态获取ip
  Future<bool> dhcpAvailable(String connection, bool enable) async {
    ProcessResult res = await Process.run('sudo', [
      'nmcli',
      'con',
      'mod',
      connection,
      'ipv4.method',
      enable ? 'auto' : 'manual',
    ]);
    // 启用dhcp后显式清空残留静态配置以避免潜在问题
    await cleanManualConfig(connection: connection);

    return res.exitCode == 0;
  }

  /// 获取网络连接信息
  Future<String?> networkConnectionInfo({required String connection}) async {
    //sudo nmcli device show $device | grep -E 'IP4.ADDRESS|IP4.GATEWAY|IP4.DNS|ipv4.method' //无法获取ipv4.method
    var command =
        "sudo nmcli con show '$connection' | grep -E 'IP4.ADDRESS|IP4.GATEWAY|IP4.DNS|ipv4.method'"; //manual auto
    var result = await Process.run('bash', ['-c', command]);
    return result.exitCode == 0 ? result.stdout.toString() : null;
  }

  /// 获取已连接网络列表
  Future<String?> connectionHistoryInfo() async {
    ProcessResult res = await Process.run('sudo', ['nmcli', 'connection']);
    return res.exitCode == 0 ? res.stdout.toString() : null;
  }

  /// 将网络设为自动连接
  Future<bool> setNetworkAutoconnect(String connection, bool auto) async {
    var command =
        "sudo nmcli connection modify '$connection' connection.autoconnect ${auto ? "yes" : "no"}";
    var result = await Process.run('bash', ['-c', command]);
    return result.exitCode == 0;
  }

  /// 开启移动网络连接
  Future<void> mobileConnectOpen({required String connection}) async {
    await setNetworkAutoconnect(connection, true);
    await connectionNetwork(connection, true);
    await setMobileDataRoaming(connection, true);
  }

  /// 是否开启移动网络漫游
  Future<bool> setMobileDataRoaming(String connection, bool isOpen) async {
    var command =
        "sudo nmcli connection modify '$connection' gsm.home-only ${isOpen ? 'no' : 'yes'}";
    var result = await Process.run('bash', ['-c', command]);
    return result.exitCode == 0;
  }

  /// 修改Mobile Network APN
  Future<bool> setMobileAPN(String connection, String apn) async {
    var command = "sudo nmcli connection modify '$connection' gsm.apn $apn";
    var result = await Process.run('bash', ['-c', command]);
    app.logW('setMobileAPN: ${result.stdout}--err: ${result.stderr}');

    return result.exitCode == 0;
  }

  /// create Mobile Network
  Future<bool> createMobileAPN({
    required String device,
    required String connection,
    required String apn,
  }) async {
    var command =
        "sudo nmcli connection add type gsm con-name '$connection' ifname $device gsm.apn $apn connection.autoconnect yes";

    var result = await Process.run('bash', ['-c', command]);
    app.logW(
      'createMobileAPN: $command-- out: ${result.stdout}--err: ${result.stderr}',
    );
    return result.exitCode == 0;
  }

  /// 设置移动网络模式
  Future<bool> setMobileNetworkMode(MobileNetWorkMode mode) async {
    var command =
        "echo 'withai@ruishu' | sudo -S mmcli -m 0 --set-allowed-modes='${mode.getMode()}'";
    var result = await Process.run('bash', ['-c', command]);
    return result.exitCode == 0;
  }

  /// 删除指定网络
  Future<bool> deleteNetwork({required String connection}) async {
    ProcessResult res = await Process.run('sudo', [
      'nmcli',
      'connection',
      'delete',
      connection,
    ]);
    return res.exitCode == 0;
  }

  /// 设备运行状态
  Future<Map<String, dynamic>> runStatus() async {
    Map<String, dynamic> statusInfo = {};

    /// cpu、内存使用率，轻量级获取概览
    var cmCommand =
        "vmstat | awk 'NR == 3 {print \$4, \$5, \$6, \$13, \$14, \$15}'";
    var cmResult = await Process.run('bash', ['-c', cmCommand]);

    if (cmResult.exitCode == 0 && cmResult.stdout.isNotEmpty) {
      List<String> cmRes = cmResult.stdout.trim().split(' ');

      statusInfo['cpu'] = {
        "us": "${cmRes[3]}%",
        "sy": "${cmRes[4]}%",
        "id": "${cmRes[5]}%",
      };
      statusInfo['mem'] = {
        "free": "${cmRes[0]}kb",
        "buff": "${cmRes[1]}kb",
        "cache": "${cmRes[2]}kb",
      };
    } else {
      app.logE('vmstat err: ${cmResult.stderr}');
    }

    /// gpu运行状态
    var gpuCommand = "nvidia-smi | awk 'NR == 10'";
    var gpuResult = await Process.run('bash', ['-c', gpuCommand]);
    if (gpuResult.exitCode == 0 && gpuResult.stdout.isNotEmpty) {
      String gpuRes = gpuResult.stdout.trim();
      List<String> parts = gpuRes.split('|').map((e) => e.trim()).toList();
      if (parts.length >= 4) {
        List<String> power = parts[1].split(RegExp(r'\s+'));
        List<String> memory = parts[2].split(RegExp(r'\s+'));
        List<String> gpu = parts[3].split(RegExp(r'\s+'));

        statusInfo['gpu'] = {
          "fan": power[0],
          "temp": power[1],
          "perf": power[2],
          "pwr": "${power[3]}/${power[5]}",
          "mem": "${memory[0]}/${memory[2]}",
          "gpu-util": gpu[0],
        };
      }
    } else {
      app.logE('gpu info error:: ${gpuResult.stderr.toString()}');
    }

    var diskCommand =
        "df -h --output=size,used,avail | sort -hr -k1 | awk 'NR == 1'";
    var diskResult = await Process.run('bash', ['-c', diskCommand]);

    if (diskResult.exitCode == 0 && diskResult.stdout.isNotEmpty) {
      List<String> diskRes = diskResult.stdout.trim().split(RegExp(r'\s+'));

      statusInfo['disk'] = {
        "size": diskRes[0],
        "used": diskRes[1],
        "avail": diskRes[2],
      };
    } else {
      app.logE('df err: ${diskResult.stderr}');
    }

    return statusInfo;
  }

  Future<double> getVideoDuration({required String path}) async {
    double duration = 0.0;
    var cmd =
        "ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 $path";
    var result = await Process.run('bash', ['-c', cmd]);
    if (result.exitCode == 0 && result.stdout.isNotEmpty) {
      final regex = RegExp(r'^[-+]?\d+(\.\d+)?$');
      if (regex.hasMatch(result.stdout.trim())) {
        duration += double.parse(result.stdout.trim());
      }
    }
    return duration;
  }

  Future<bool> getVideoFrame({
    required String videoPath,
    required String framePath,
    required int seconds,
  }) async {
    String cmd =
        'ffmpeg -hwaccel cuda -ss ${seconds.toHMS} -i $videoPath -vframes 1 -q:v 31 $framePath'; //-c:v copy
    var result = await Process.run('bash', ['-c', cmd]);
    if (result.exitCode == 0) {
      return true;
    }
    return false;
  }

  Future<void> playAudio({
    required String deviceName,
    required String audioPath,
    required int volume,
  }) async {
    var cmd = "paplay --volume $volume -d $deviceName $audioPath";
    await Process.run('bash', ['-c', cmd]);
    //cvlc --aout pulse --alsa-audio-device=alsa_output.pci-0000_00_1f.3.analog-stereo --play-and-exit --no-loop --gain 1.0 ~/surgsmart-operating-room/data/flutter_assets/packages/surgsmart/assets/audio/mark_screenshot.wav
  }

  Future<bool> mergeVideo({
    required String fileList,
    required String outputPath,
  }) async {
    Directory outputDir = Directory(
      outputPath.substring(0, outputPath.lastIndexOf('/')),
    );
    if (!outputDir.existsSync()) {
      outputDir.createSync(recursive: true);
    }
    String cmd =
        "echo 'withai@ruishu' | sudo -S ffmpeg -y -f concat -safe 0 -i $fileList -c copy $outputPath";
    var result = await Process.run('bash', ['-c', cmd]);
    if (result.exitCode == 0) {
      return true;
    }
    app.logE("offline 本地视频合并失败：${result.stderr}");

    return false;
  }

  Future<bool> startAi({
    required int surgeryId,
    required String procedureCode,
  }) async {
    String containerName = 'algorithm-analyser-$surgeryId';
    try {
      if (await isContainerExists(containerName)) {
        app.logI("容器存在: $surgeryId $procedureCode\n");
        return true;
      }
    } catch (e) {
      app.logE("检查算法容器err: $e\n");
    }
    String videoDevicePath = await DeviceCmd.share.videoPathInfo();

    String algoHistoryDataDir = "/home/<USER>/Documents/v202310/algo_history/";
    String algoSavedFramesDir = "/home/<USER>/Documents/v202310/saved_frames/";
    Directory hisDir = Directory(algoHistoryDataDir);
    if (!hisDir.existsSync()) {
      hisDir.createSync(recursive: true);
    }
    Directory savedDir = Directory(algoSavedFramesDir);
    if (!savedDir.existsSync()) {
      savedDir.createSync(recursive: true);
    }
    //insert into tdengine
    if (!await HttpTDEngineApi.insertToTdEngine(
      surgeryId,
      procedureCode.toUpperCase(),
    )) {
      return false;
    }
    // ApiModel apiModel =
    //     await HttpUpgradeApi<ApiModel>.getTokenAndDecryptKey().request();
    // String token =
    //     "${apiModel.map?['type']}:${apiModel.map?['version']}:${apiModel.map?['build']}";//
    // String? decryptKey = apiModel.map?["decrypt_key"];

    // todo 这里暂时写死，需要获取算法信息和解密key
    String token = ""; //2:1.0.0:93
    String? decryptKey = "";

    String cmdStr =
        "python3 main.py -c ${surgeryId}_${procedureCode.toUpperCase()} -d ${AppContext.share.authorizeInfo.deviceId}"; //
    if (decryptKey.isNotEmpty == true) {
      cmdStr += " -t $token -k $decryptKey";
    }
    cmdStr += " --online 1";
    //  -v $MODEL_DIR:/models:ro \

    String cmd = '''
docker run -d \\
--name $containerName \\
--network host \\
--device $videoDevicePath:/dev/video0 \\
--gpus all \\
-v $algoHistoryDataDir:/history:rw \\
-v $algoSavedFramesDir:/frames:rw \\
-e NVIDIA_VISIBLE_DEVICES=all \\
-e MODEL_DIR=/models \\
-e HISTORY_DATA_DIR=/history \\
-e SAVED_FRAMES_DIR=/frames \\
-e MQTT_LOCAL_BROKER=********** \\
-e MQTT_LOCAL_PORT=1883 \\
-e MQTT_CLOUD_BROKER=${AppConfig.remoteMqttUri.host} \\
-e MQTT_CLOUD_PORT=${AppConfig.remoteMqttUri.port} \\
-e TDENGINE_HOST=********** \\
-e TDENGINE_PORT=6030 \\
algorithm-analyser:local \\
$cmdStr
''';
    ProcessResult result = await Process.run('bash', ['-c', cmd]);
    app.logI("启动算法容器cmd: $cmd\n");

    if (result.exitCode == 0) {
      processPids[containerName] = result.pid;
      return true;
    }
    app.logI("启动算法容器result:${result.stderr},${result.stdout}");
    return false;
  }

  Future<bool> stopAi({required int surgeryId}) async {
    String algoHistoryData =
        "/home/<USER>/Documents/v202310/algo_history/$surgeryId.json";
    File algoHistoryFile = File(algoHistoryData);
    if (algoHistoryFile.existsSync()) {
      algoHistoryFile.delete();
    }
    await killAllContainers();
    String containerName = 'algorithm-analyser-$surgeryId';
    int? pid = processPids[containerName];
    if (pid == null) return true;
    processPids.remove(containerName);
    return Process.killPid(pid);
  }

  /// 检查 Docker 容器是否存在
  /// [containerName] 要检查的容器名称
  /// [runningOnly] 是否只检查正在运行的容器
  Future<bool> isContainerExists(
    String containerName, {
    bool runningOnly = true,
  }) async {
    try {
      // 执行 docker ps 命令
      final result = await Process.run('docker', [
        'ps',
        if (!runningOnly) '-a', // 添加 -a 参数获取所有容器（包含已停止的）
        '--filter', 'name=^/$containerName\$', // 精确匹配容器名
        '--format', '{{.Names}}', // 仅输出容器名称
      ]);
      app.logE("容器列表: ${result.stdout.toString()}");

      // 检查执行结果
      if (result.exitCode != 0) {
        throw Exception('Docker command failed: ${result.stderr}');
      }

      // 解析输出结果
      final output = (result.stdout as String).trim();
      return output.contains(containerName);
    } on ProcessException catch (e) {
      throw Exception('Failed to execute docker command: $e');
    }
  }

  /// 强制删除所有名称以指定前缀开头的 Docker 容器
  Future<void> killAllContainers() async {
    try {
      // 获取所有容器列表（包含已停止的）
      final listResult = await Process.run('docker', [
        'ps',
        '-a',
        '--format',
        '{{.Names}}',
      ]);

      if (listResult.exitCode != 0) {
        throw Exception('Failed to list containers: ${listResult.stderr}');
      }

      // 解析容器名称
      final containers =
          (listResult.stdout as String)
              .split('\n')
              .where((name) => name.isNotEmpty)
              .toList();

      // 过滤目标容器
      final targetContainers =
          containers
              .where((name) => name.startsWith("algorithm-analyser-"))
              .toList();

      // 逐个删除容器
      for (final containerName in targetContainers) {
        try {
          app.logD("[INFO] Removing container: $containerName");
          final rmResult = await Process.run('docker', [
            'rm',
            '-f',
            containerName,
          ]);

          if (rmResult.exitCode != 0) {
            app.logE(
              "[ERROR] Failed to remove $containerName: ${rmResult.stderr}",
            );
          } else {
            app.logD("[INFO] Successfully removed: $containerName");
          }
        } catch (e) {
          app.logE("[ERROR] Error removing $containerName: $e");
        }
      }
    } catch (e) {
      app.logE("[ERROR] Error killing containers: $e");
    }

    app.logD("[INFO] Leave killAllContainers()");
  }

  Future<bool> badVideoRepair(String workingVideo, String brokenVideo) async {
    final result = await Process.run('untrunc', [workingVideo, brokenVideo]);

    if (result.exitCode != 0) {
      app.logE("[ERROR] Failed to repair $brokenVideo: ${result.stderr}");
      return false;
    } else {
      app.logD("[INFO] Successfully repaired: $brokenVideo");
      return true;
    }
  }

  Future<void> videoInfoRepair(String path) async {
    final result = await Process.run('video_repair', [path]);

    if (result.exitCode != 0) {
      app.logE("[ERROR] Failed to repair ${result.stderr},path:$path");
    }
  }

  Future<String> getAlgorithmVersion() async {
    var command =
        "docker run --rm algorithm-analyser:local cat VERSION | awk -F= '/version|build/ {print \$2}' | paste -sd+ - # 1.0.0+117";
    var res = await Process.run('bash', ['-c', command]);

    if (res.exitCode != 0) {
      app.logE("[ERROR] getAlgorithmVersion ${res.stderr}");
    }
    return res.stdout.toString().trim();
  }
}
