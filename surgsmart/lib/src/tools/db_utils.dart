import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:isar/isar.dart';
import 'package:surgsmart/src/models/isar/surgery_features.model.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';

enum TableSchema {
  surgery(OfflineSurgeryDataSchema),
  features(SurgeryFeaturesSchema);

  /// 表schema
  final CollectionSchema<dynamic> schema;

  const TableSchema(this.schema);
}

class DbUtils {
  DbUtils._();

  static final instance = DbUtils._();
  factory DbUtils() => instance;

  Isar get isar => _isar;

  late Isar _isar;

  //暂存当前手术信息
  OfflineSurgeryData? currentSurgeryInfo;

  /// 初始化建立连接
  Future<void> init() async {
    String dbPath = '${AppContext.share.documentsPath}/.surgsmart_db/';

    try {
      final dir = Directory(dbPath);
      if (!await dir.exists()) {
        await dir.create();
      }
      _isar = await Isar.open(
        [TableSchema.surgery.schema, TableSchema.features.schema],
        directory: dbPath,
      );
    } catch (e) {
      app.logE('Error opening database: $e');
    }
  }

/*********手术记录常用查询 *********/
  /// 查询所有手术数量
  Future<int> querySurgeryCount() async {
    return await isar.offlineSurgeryDatas.count();
  }

  /// 查询最近一次手术记录
  Future<OfflineSurgeryData?> queryRecentSurgeryRecord() async {
    final surgery = await isar.offlineSurgeryDatas
        .where()
        .sortByLocalSurgeryIdDesc()
        .findFirst();
    return surgery;
  }

  /// 查询大于指定时间且未被删除的手术记录
  Future<List<OfflineSurgeryData>> queryDaysAgoSurgeryRecord(int days) async {
    final surgers = await isar.offlineSurgeryDatas
        .filter()
        .recordStatusEqualTo(0)
        .createTimeLessThan(DateTime.now().subtract(Duration(days: days)))
        .findAll();
    return surgers;
  }

  /// 查询手术记录
  Future<OfflineSurgeryData?> querySurgeryDataById(int localSurgeryId) async {
    final surgery = await isar.offlineSurgeryDatas
        .filter()
        .localSurgeryIdEqualTo(localSurgeryId)
        .findFirst();
    return surgery;
  }

  // 软删除，将recordStatus字段设置为1
  Future<void> softDeleteSurgery(List<OfflineSurgeryData> datas) async {
    if (datas.isEmpty) return;
    for (var element in datas) {
      element.recordStatus = 1;
    }
    await put(datas);
  }

//*******以下通用方法 ********/
  /// 插入或更新数据
  Future<List<Id>> put(List<dynamic> datas) async {
    if (datas.isEmpty) return [];
    return await isar.writeTxn<List<Id>>(() async {
      if (datas.first is OfflineSurgeryData) {
        return await isar.offlineSurgeryDatas
            .putAll(datas.cast<OfflineSurgeryData>());
      } else if (datas.first is SurgeryFeatures) {
        return await isar.surgeryFeatures.putAll(datas.cast<SurgeryFeatures>());
      }
      return [];
    });
  }

  /// 删除所有数据
  Future<void> deleteAll(TableSchema table) async {
    await isar.writeTxn(() async {
      switch (table) {
        case TableSchema.features:
          await isar.surgeryFeatures.clear();
          break;
        case TableSchema.surgery:
          await isar.offlineSurgeryDatas.clear();
          break;
      }
    });
  }

  /// 删除指定记录
  Future<void> delete(List<dynamic> datas) async {
    if (datas.isEmpty) return;

    await isar.writeTxn(() async {
      if (datas.first is OfflineSurgeryData) {
        List<Id> ids = datas.map((surgery) => surgery.id).toList().cast<Id>();
        return await isar.offlineSurgeryDatas.deleteAll(ids);
      } else if (datas.first is SurgeryFeatures) {
        List<Id> ids = datas.map((surgery) => surgery.id).toList().cast<Id>();
        return await isar.surgeryFeatures.deleteAll(ids);
      }
    });
  }
}
