import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:dio/dio.dart';

class UploadTask {
  final String url;
  final String filePath;
  final int? sliceSize;
  final Function(int, int)? onProgress;
  final Function(String)? onError;
  CancelToken? cancelToken;

  UploadTask({
    required this.url,
    required this.filePath,
    this.onProgress,
    this.onError,
    this.sliceSize,
    this.cancelToken,
  });
}

class UploadManager {
  static final timeoutDuration = Duration(seconds: 600);

  final _dio = Dio(
    BaseOptions(
      connectTimeout: timeoutDuration,
      sendTimeout: timeoutDuration,
      receiveTimeout: timeoutDuration,
    ),
  );
  // ..interceptors.add(
  //   LogInterceptor(responseBody: true, requestHeader: true, request: true),
  // );

  /// 单例模式管理上传任务
  UploadManager._internal();
  static final UploadManager _instance = UploadManager._internal();
  factory UploadManager() => _instance;
  static get instance => _instance;

  ///上传文件队列
  final Queue<UploadTask> taskQueue = Queue();

  /// 移除上传任务
  void removeUpload(String filePath) {
    taskQueue.removeWhere((element) => element.filePath == filePath);
  }

  /// 清空上传任务
  void clearUpload() {
    taskQueue.clear();
  }

  /// 获取待上传文件列表
  List<String> get unUploadedList => taskQueue.map((e) => e.filePath).toList();

  /// 取消存在进度的任务
  void cancelUploading() {
    for (var task in taskQueue) {
      if (task.onProgress != null) {
        task.cancelToken?.cancel();
        task.cancelToken = null;
      }
    }
  }

  /// 上传文件
  Future<bool> uploadFile(UploadTask task) async {
    // int startOffset = 0;
    // if (task.sliceSize != null) {
    //   //限制数据块大小
    // } else {}

    // MultipartFile multipartFile = await MultipartFile.fromFile(task.filePath);
    // FormData formData = FormData.fromMap({
    //   "file": multipartFile,
    // });

    File file = File(task.filePath);
    if (!await file.exists()) return false;
    task.cancelToken ??= CancelToken();
    FileStat fileStat = await file.stat();
    bool upResult = false;
    String errMsg = "";
    try {
      Uri uri = Uri.parse(task.url);
      taskQueue.addFirst(task);
      var response = await _dio.put(
        task.url,
        data: file.openRead(),
        onSendProgress: task.onProgress,
        cancelToken: task.cancelToken,
        options: Options(
          headers: {
            'Content-Length': fileStat.size.toString(),
            'Host': uri.host,
            'Accept': '*/*',
            'Expect': '100-continue',
          },
        ),
      );
      if (response.statusCode == 200) {
        upResult = true;
      } else {
        errMsg =
            "statusMessage:${response.statusMessage},response:${response.data.toString()}";
      }
    } catch (e) {
      errMsg = e.toString();
    }
    if (upResult) {
      taskQueue.removeWhere((item) => item.filePath == task.filePath);
      if (taskQueue.isNotEmpty) {
        uploadFile(taskQueue.first);
      }
    } else {
      app.logE(errMsg);
      task.onError?.call(errMsg);
    }
    return upResult;
  }
}
