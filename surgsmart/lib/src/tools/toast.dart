import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum ToastType { success, error, warning, info }

class ToastUtils {
  //static OverlayEntry? _overlayEntry;
  static void showToast({
    String? message,
    double? fontSize,
    ToastType? type,
    int? delayed,
  }) {
    var widget = Positioned(
      top: 48.h,
      width: app.width,
      child: CustomToast(
        message: message.toString(),
        fontSize: fontSize,
        type: type,
      ),
    );

    app.showToast(child: widget);

    /** 废弃使用OverlayEntry方式，偶现toast无法显示且难以复现排查，使用上述自定义Stack实现toast **/
    // if (_overlayEntry != null) {
    //   _overlayEntry!.remove();
    //   _overlayEntry = null;
    // }
    //
    // _overlayEntry = OverlayEntry(
    //   builder: (context) => Positioned(
    //     top: 48.h,
    //     width: MediaQuery.of(context).size.width,
    //     child: Material(
    //       color: Colors.transparent,
    //       child: CustomToast(
    //         message: message.toString(),
    //         fontSize: fontSize,
    //         type: type,
    //       ),
    //     ),
    //   ),
    // );
    // Overlay.of(context).insert(_overlayEntry!);
    // Future.delayed(
    //   Duration(seconds: delayed ?? 2),
    //   () {
    //     if (_overlayEntry != null) {
    //       _overlayEntry!.remove();
    //       _overlayEntry = null;
    //     }
    //   },
    // );
  }
}

class CustomToast extends StatelessWidget {
  final String message;
  final double? fontSize;
  final ToastType? type;

  const CustomToast({
    super.key,
    required this.message,
    this.type,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    var iconColor = Colors.white;
    IconData? icon;
    if (type != null) {
      switch (type) {
        case ToastType.info:
          iconColor = const Color(0xff0ec6d2);
          icon = Icons.error_rounded;
          break;
        case ToastType.success:
          iconColor = const Color(0xff23d9bd);
          icon = Icons.check_circle_rounded;
          break;
        case ToastType.error:
          iconColor = const Color(0xffff4e4e);
          icon = Icons.error_rounded;
          break;
        case ToastType.warning:
          iconColor = const Color(0xffff4e4e);
          icon = Icons.warning_amber_rounded;
          break;
        default:
      }
    }
    return Row(
      children: [
        const Spacer(),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 48.w, vertical: 24.h),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.4),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (icon != null) Icon(icon, color: iconColor, size: 72.sp),
              SizedBox(width: 12.w),
              Text(
                message,
                style: TextStyle(
                  fontSize: fontSize ?? 48.sp,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const Spacer(),
      ],
    );
  }
}
