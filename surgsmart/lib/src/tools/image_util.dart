import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ImageUtil {
  static Future<Uint8List> convertImageToNV12(ui.Image image) async {
    final ByteData? rgbaData = await image.toByteData(
      format: ui.ImageByteFormat.rawRgba,
    );
    final int width = image.width;
    final int height = image.height;

    return rgbaToNV12(rgbaData!, width, height);
  }

  static Future<Uint8List> rgbaToNV12(
    ByteData rgbaData,
    int width,
    int height,
  ) async {
    final Uint8List rgbaBytes = rgbaData.buffer.asUint8List();

    // 创建 NV12 缓冲区（Y 平面 + UV 平面）
    final int ySize = width * height;
    final int uvSize = (width * height) ~/ 2;
    final Uint8List nv12Buffer = Uint8List(ySize + uvSize * 2);

    // 遍历每个像素，转换为 YUV
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int rgbaIndex = (y * width + x) * 4;
        final int r = rgbaBytes[rgbaIndex];
        final int g = rgbaBytes[rgbaIndex + 1];
        final int b = rgbaBytes[rgbaIndex + 2];

        // RGB 转 YUV 公式（ITU-R BT.601）
        final int Y = ((66 * r + 129 * g + 25 * b + 128) >> 8) + 16;
        final int U = ((-38 * r - 74 * g + 112 * b + 128) >> 8) + 128;
        final int V = ((112 * r - 94 * g - 18 * b + 128) >> 8) + 128;

        // Y 平面
        nv12Buffer[y * width + x] = Y.clamp(0, 255);

        // UV 平面（每 2x2 块采样一次）
        if (y % 2 == 0 && x % 2 == 0) {
          final int uvIndex =
              ySize + (y ~/ 2) * (width ~/ 2) * 2 + (x ~/ 2) * 2;
          nv12Buffer[uvIndex] = U.clamp(0, 255);
          nv12Buffer[uvIndex + 1] = V.clamp(0, 255);
        }
      }
    }

    return nv12Buffer;
  }

  /// 将image上叠加其他图片
  /// [backgroundImage] 原始图片的Image
  /// [overlayImage]叠加图片的Image
  /// [offset] 叠加图片在原始图片上的坐标 (x, y)
  static Future<ui.Image> overlayImageOnCanvas({
    required ui.Image bgImage,
    required List<OverlayImageInfo> overlayImages,
  }) async {
    // 创建 Canvas 和 Paint
    final recorder = ui.PictureRecorder();
    final canvas = ui.Canvas(
      recorder,
      ui.Rect.fromLTRB(
        0,
        0,
        bgImage.width.toDouble(),
        bgImage.height.toDouble(),
      ),
    );

    // 绘制背景图片
    canvas.drawImage(bgImage, ui.Offset.zero, ui.Paint());

    // 在指定坐标绘制叠加图片+label描述
    for (var overlayInfo in overlayImages) {
      double realCursorX = overlayInfo.offset.dx * bgImage.width;
      double realCursorY = overlayInfo.offset.dy * bgImage.height;
      canvas.drawImage(
        overlayInfo.image,
        ui.Offset(realCursorX, realCursorY),
        ui.Paint()..blendMode = ui.BlendMode.srcOver, // 默认混合模式
      );

      // 绘制文本
      const textColor = Colors.white;
      //final fontSize = 74.sp;
      final fontSize = overlayInfo.image.width / 4;
      const backgroundColor = Color(0xff2a69e9);
      const paintingStyle = PaintingStyle.fill;
      final width = 2.w;
      final padding = EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h);
      final style = TextStyle(
        color: textColor,
        fontSize: fontSize,
        height: 1.3,
      );
      final span = TextSpan(text: overlayInfo.label, style: style);
      final painter = TextPainter(text: span, textDirection: TextDirection.ltr);
      painter.layout(minWidth: 0, maxWidth: bgImage.width.toDouble());
      double x =
          realCursorX + (overlayInfo.image.width - painter.width) / 2 + 64.w;
      double y = realCursorY + overlayInfo.image.height + 12.h;
      final position = Offset(x, y);
      final backgroundPaint =
          Paint()
            ..color = backgroundColor
            ..style = paintingStyle
            ..strokeWidth = width;
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            x - padding.left,
            y - padding.bottom,
            painter.width + padding.horizontal,
            painter.height + padding.vertical,
          ),
          Radius.circular(16.r),
        ),
        backgroundPaint,
      );
      painter.paint(canvas, position);
    }

    // 生成合并后的图片
    final ui.Picture picture = recorder.endRecording();
    final ui.Image mergedImage = await picture.toImage(
      bgImage.width,
      bgImage.height,
    );

    return mergedImage;
  }

  /// 辅助函数：将 Uint8List 解码为 ui.Image
  Future<ui.Image> bytesToImage(Uint8List bytes, int width, int height) async {
    final completer = Completer<ui.Image>();
    ui.decodeImageFromPixels(
      bytes,
      width,
      height,
      ui.PixelFormat.rgba8888,
      (ui.Image image) => completer.complete(image),
    );
    return completer.future;
  }
}

class OverlayImageInfo {
  final ui.Image image;
  final Offset offset;
  final String label;

  OverlayImageInfo({
    required this.image,
    required this.offset,
    this.label = '',
  });
}
