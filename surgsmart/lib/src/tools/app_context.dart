import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;
import 'dart:math';
import 'dart:ui' as ui;
import 'package:app_foundation/app_foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/apis/app_config.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';
import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';
import 'package:surgsmart/src/models/network_device_model.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';

enum TaskState {
  synergyOrLive, //协同或直播
  upload, //上传
  download, //下载
  install, //安装更新
  nothing, //空闲
}

class AppContext {
  AppContext._();

  static final share = AppContext._();

  /// 机器码(本地化部署过程中可能会重复生成)
  late String machineCode;

  /// 授权码
  late String licenseCode;

  /// 大手指图标
  late final ui.Image fingerLarge;

  /// 小手指图标
  late final ui.Image fingerSmall;

  /// 接口授权信息
  late AuthorizeInfo authorizeInfo;

  /// 遥控器认证令牌
  final controlToken = AppControlToken();

  /// 网络是否连通
  final networkConnected = true.notifier;

  /// Mqtt是否连通
  final mqttConnected = false.notifier;

  /// 蓝牙是否连接
  final bleConnected = false.notifier;

  ///蓝牙开关状态
  final bleAdapterState = true.notifier;

  /// 启用蓝牙遥控,false则为网络mqtt遥控
  final enableBleControl = false;

  /// 是否启用私有服务器
  static final enablePrivateServer = true;

  /// 当前正在进行的任务状态，提供逻辑判断
  var taskState = TaskState.nothing;

  /// 网络连接类型
  List<ConnectivityResult> _networkConnectivity = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();

  /// 当前可用的网络类型
  List<ConnectivityResult> get networkConnectivity => _networkConnectivity;

  /// 所有网络设备状态
  final List<NetworkDevice> _networkDevices = [];

  List<NetworkDevice> get networkDevices => _networkDevices;

  ///记录上次网络状态列表用以判定网络是否存在变化
  List<NetworkDevice> lastNetworkDevices = [];

  static bool multiWindow = false; //当前是否是扩展窗口

  static final timeoutDuration = Duration(seconds: 10);
  Dio? _dio;

  /// 记录Documents路径，避免多处await获取
  late final String documentsPath;

  final events = <int, EventAttrInfo>{};

  final insightFullMap = <int, dynamic>{};


  /// 初始化
  Future<void> init() async {
    documentsPath = (await getApplicationDocumentsDirectory()).path;
    // 多窗口插件存在

    try {
      await readLicenseCode();

      final fingerLargeImage = await rootBundle.load(
        R.image.finger_large().keyName,
      );
      fingerLarge = await decodeImageFromList(
        fingerLargeImage.buffer.asUint8List(),
      );

      final fingerSmallImage = await rootBundle.load(
        R.image.finger_small().keyName,
      );
      fingerSmall = await decodeImageFromList(
        fingerSmallImage.buffer.asUint8List(),
      );
    } catch (error) {
      machineCode = "";
      licenseCode = "";
      app.logE(error.toString());
    }

    loadInsight();

    if (AppContext.share.enableBleControl) {
      /// 开启蓝牙 ble
      // final isSuccess = await PeripheralController.instance.init();
      // if (isSuccess) {
      //   app.logI("蓝牙初始化成功");
      // } else {
      //   app.logE("蓝牙初始化失败");
      // }
    }

    /// 监听网络连接状态
    _connectivity.onConnectivityChanged.listen(checkNetWorkConnect);

    /// 首次检测网络状态
    checkNetWorkConnect();
  }

  /// 加载算法数据
  Future<void> loadInsight() async {
    final jsonText = await R.text.insight_json();
    final insight = json.decode(jsonText);
    final eventList = insight['common']['event'];

    for (final element in eventList) {
      final eventAttr = EventAttrInfo().initWith(element);
      events[eventAttr.value] = eventAttr;
    }

    insight.forEach((proc, featureMap) {
      featureMap.forEach((featureType, features) {
        for (var feature in features) {
          insightFullMap[feature["value"]] = {
            "procedure": proc,
            "feature": feature,
            "feature_type": featureType,
          };
        }
      });
    });
  }

  /// 检测是否可访问互联网
  Future<void> checkNetWorkConnect([List<ConnectivityResult>? results]) async {
    //app.logW("网络状态改变: ${results?.toString()}");

    getNetWorkDevices();
    if (AppContext.enablePrivateServer &&
        AppPreferences.privateServerUrl.stringValue == null) {
      return;
    }

    try {
      _networkConnectivity = results ?? await _connectivity.checkConnectivity();
      //不能凭借连接状态判定可靠
      networkConnected.value = getNetWorkStatus() && await netWorkRequest();
    } catch (error) {
      networkConnected.value = false;
      app.logE("网络连接丢失: $error");
    }

    if (enableBleControl) {
      // PeripheralController.instance.notify(
      //   msgType: ClientMsgType.networkState.index,
      //   data: jsonEncode({"enabled": networkConnected.value}),
      // );
    }
  }

  Future<bool> netWorkRequest({bool refreshOptions = false}) async {
    _dio ??= Dio(
      BaseOptions(
        baseUrl: AppConfig.apiUrl.origin,
        connectTimeout: timeoutDuration,
        sendTimeout: timeoutDuration,
        receiveTimeout: timeoutDuration,
        contentType: 'application/json',
      ),
    );
    if (refreshOptions) {
      _dio!.options.baseUrl = AppConfig.apiUrl.origin;
    }
    try {
      final response = await _dio!.get("${AppConfig.apiUrl}/systime/");
      return response.statusCode != null && response.statusCode! / 100 == 2;
    } catch (e) {
      rethrow;
    }
  }

  /// 获取所有网络设备类型及状态
  Future<List<NetworkDevice>> getNetWorkDevices() async {
    return [];
    // String res = await DeviceCmd.share.getAllNetWorkStatus();
    // _networkDevices = parseNetworkDevices(res);
    // if (_networkDevices.toString() != lastNetworkDevices.toString()) {
    //   eventBus.fire(EventBusInfo(type: EventBusType.netWorkChange));

    //   /// 判断有线网络可用状态（如未插入和拔出状态监听）
    //   final lastEthernetStates =
    //       lastNetworkDevices
    //           .where((device) => device.type == NetWorkType.ethernet.name)
    //           .toList();
    //   final currentEthernetStates =
    //       _networkDevices
    //           .where((device) => device.type == NetWorkType.ethernet.name)
    //           .toList();

    //   bool lastAvailable = true;
    //   bool currentAvailable = true;
    //   if (currentEthernetStates.isEmpty ||
    //       currentEthernetStates.every((dev) => !dev.isAvailable())) {
    //     currentAvailable = false;
    //   }
    //   if (lastEthernetStates.isEmpty ||
    //       lastEthernetStates.every((dev) => !dev.isAvailable())) {
    //     lastAvailable = false;
    //   }
    //   app.logW("上次网络状态: $lastAvailable，当前网络状态: $currentAvailable");
    //   if (lastAvailable != currentAvailable) {
    //     eventBus.fire(
    //       EventBusInfo(
    //         type: EventBusType.ethernetAvailable,
    //         data: currentAvailable,
    //       ),
    //     );
    //   }
    // }
    // lastNetworkDevices = _networkDevices;
    // return _networkDevices;
  }

  /// 获取所有已连接的网络设备及类型
  Future<List<NetworkDevice>> getConnectedNetWorks() async {
    List<NetworkDevice> connectedDevices =
        _networkDevices.where((element) => element.isConnected()).toList();
    return connectedDevices;
  }

  List<NetworkDevice> parseNetworkDevices(
    String input, {
    bool isConnectionParse = false,
  }) {
    List<NetworkDevice> devices = [];
    List<String> lines = input.trim().split('\n').skip(1).toList();
    if (isConnectionParse) {
      // 定义UUID的正则
      final uuidRegex = RegExp(
        r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
      );

      for (var line in lines) {
        final uuidMatch = uuidRegex.firstMatch(line);
        if (uuidMatch != null) {
          // String uuid = uuidMatch.group(0)!;

          // 拆分：NAME 在 UUID 前；TYPE 和 DEVICE 在 UUID 后
          int uuidStart = uuidMatch.start;
          int uuidEnd = uuidMatch.end;
          String afterUuid = line.substring(uuidEnd).trim();

          String connection = line.substring(0, uuidStart).trim();
          String type = '';
          String device = '';
          // 分离 TYPE 和 DEVICE
          List<String> parts = afterUuid.split(RegExp(r'\s+'));
          if (parts.length >= 2) {
            type = parts[0];
            device = parts[1];
          }

          devices.add(
            NetworkDevice(
              device: device.trim(),
              type: type.trim(),
              state: 'disconnected',
              connection: connection.trim(),
            ),
          );
        }
      }
    } else {
      RegExp regExp = RegExp(r'(\S+)\s+(\S+)\s+(\S+)\s+(.*)');
      for (String line in lines) {
        Match? match = regExp.firstMatch(line);
        if (match != null) {
          String device = match.group(1) ?? '';
          String type = match.group(2) ?? '';
          String state = match.group(3) ?? '';
          String connection = match.group(4) ?? '';

          devices.add(
            NetworkDevice(
              device: device.trim(),
              type: type.trim(),
              state: state.trim(),
              connection: connection.trim(),
            ),
          );
        }
      }
    }

    return devices;
  }

  NetworkConfig parseNetworkConfig(String rawOutput) {
    final config = NetworkConfig();
    final lines = rawOutput.split('\n');

    // 正则表达式匹配模式
    final methodRegex = RegExp(r'^ipv4\.method:\s+(\w+)');
    final addressRegex = RegExp(
      r'IP4\.ADDRESS\[\d+\]:\s+(\d+\.\d+\.\d+\.\d+)/(\d+)',
    );
    final gatewayRegex = RegExp(r'IP4\.GATEWAY:\s+(\d+\.\d+\.\d+\.\d+)');
    final dnsRegex = RegExp(r'IP4\.DNS\[\d+\]:\s+(\d+\.\d+\.\d+\.\d+)');

    for (final line in lines) {
      // 匹配 IP 地址和子网掩码（CIDR）
      if (methodRegex.hasMatch(line)) {
        final match = methodRegex.firstMatch(line);
        config.method = match?.group(1) ?? '';
      } else if (addressRegex.hasMatch(line)) {
        // 匹配 IP 地址和子网掩码（CIDR）
        final match = addressRegex.firstMatch(line);
        if (match != null) {
          config.ip.add(match.group(1) ?? '');
          config.subnetMask.add(
            config.subnetMaskDotDecimal(
              int.tryParse(match.group(2) ?? '') ?? 0,
            ),
          );
          //config.subnetMask.add(match.group(2) ?? '');
        }
      }
      // 匹配网关
      else if (gatewayRegex.hasMatch(line)) {
        final match = gatewayRegex.firstMatch(line);
        config.gateway = match?.group(1) ?? '';
      }
      // 匹配 DNS 服务器
      else if (dnsRegex.hasMatch(line)) {
        final match = dnsRegex.firstMatch(line);
        if (match != null) {
          config.dnsServers.add(match.group(1) ?? '');
        }
      }
    }

    return config;
  }

  //获取网络状态
  bool getNetWorkStatus() {
    //app.logI("网络状态: $networkConnectivity");
    //仅支持有线和蜂窝网络,linux上插件无法识别mobile/gsm,展示为other
    if (networkConnectivity.contains(ConnectivityResult.mobile) ||
        networkConnectivity.contains(ConnectivityResult.ethernet) ||
        networkConnectivity.contains(ConnectivityResult.other) ||
        networkConnectivity.contains(ConnectivityResult.wifi)) {
      return true;
    }
    return false;
  }

  AssetResource getNetworkStatusIconByType(NetWorkType type) {
    if (!networkConnected.value) R.svg.asset.network_none; //无网络都用此图标
    if (type == NetWorkType.ethernet) {
      return networkConnected.value
          ? R.svg.asset.wired_connected
          : R.svg.asset.wired_disconnected;
    } else if (type == NetWorkType.wifi) {
      return networkConnected.value
          ? R.svg.asset.wifi_connected
          : R.svg.asset.wifi_disconnected;
    } else if (type == NetWorkType.gsm) {
      return networkConnected.value
          ? R.svg.asset.cellular_connected
          : R.svg.asset.cellular_disconnected;
    } else {
      return R.svg.asset.network_none;
    }
  }

  /// 获取当前网络状态图标
  AssetResource getNetWorkStatusIcon({String? networkType}) {
    String? selectedNetwork =
        networkType ?? AppPreferences.selectedNetwork.stringValue;
    if (selectedNetwork == null) {
      if (networkConnectivity.contains(ConnectivityResult.ethernet)) {
        return getNetworkStatusIconByType(NetWorkType.ethernet);
      } else if (networkConnectivity.contains(ConnectivityResult.wifi)) {
        return getNetworkStatusIconByType(NetWorkType.wifi);
      } else if (networkConnectivity.contains(ConnectivityResult.other) &&
          networkConnected.value) {
        return getNetworkStatusIconByType(NetWorkType.gsm);
      } else {
        return getNetworkStatusIconByType(NetWorkType.none);
      }
    } else if (selectedNetwork == NetWorkType.ethernet.name) {
      return getNetworkStatusIconByType(NetWorkType.ethernet);
    } else if (selectedNetwork == NetWorkType.wifi.name) {
      return getNetworkStatusIconByType(NetWorkType.wifi);
    } else if (selectedNetwork == NetWorkType.gsm.name) {
      return getNetworkStatusIconByType(NetWorkType.gsm);
    }
    return getNetworkStatusIconByType(NetWorkType.none);
  }

  Future<NetworkConfig?> getNetworkConfig({required String connection}) async {
    String? resOutput = await DeviceCmd.share.networkConnectionInfo(
      connection: connection,
    );
    if (resOutput == null) return null;
    return parseNetworkConfig(resOutput);
  }

  Future<List<NetworkDevice>> getConnectionHistory() async {
    String? resOutput = await DeviceCmd.share.connectionHistoryInfo();
    if (resOutput == null) return [];

    return parseNetworkDevices(resOutput, isConnectionParse: true);
  }

  /// 切换网络
  Future<void> switchNetwork(NetWorkType type) async {
    // if (type == NetWorkType.none) {
    //   await switchAllEthernet(false);
    //   await DeviceCmd.share.setWifiEnable(false);
    //   await DeviceCmd.share.setMobileEnable(false);
    // } else if (type == NetWorkType.ethernet) {
    //   //如果是开启则需关闭其他网络
    //   await DeviceCmd.share.setWifiEnable(false);
    //   await DeviceCmd.share.setMobileEnable(false);
    //   await switchAllEthernet(true);
    // } else if (type == NetWorkType.wifi) {
    //   await DeviceCmd.share.setWifiEnable(true);
    //   await DeviceCmd.share.setMobileEnable(false);
    //   await switchAllEthernet(false);
    // } else if (type == NetWorkType.gsm) {
    //   await DeviceCmd.share.setMobileEnable(true);
    //   Future.delayed(Duration(seconds: 3)).then((val) async {
    //     List<NetworkDevice> historyConnection =
    //         await AppContext.share.getConnectionHistory();
    //     if (historyConnection.indexWhere(
    //           (element) =>
    //               element.connection ==
    //               NetworkController.defaultMobileNetworkName,
    //         ) !=
    //         -1) {
    //       DeviceCmd.share.mobileConnectOpen(
    //         connection: NetworkController.defaultMobileNetworkName,
    //       );
    //     }
    //   });
    //   await DeviceCmd.share.setWifiEnable(false);
    //   await switchAllEthernet(false);
    // }
  }

  /// 连接或断开所有有线网络
  Future<void> switchAllEthernet(bool isOpen) async {
    List<NetworkDevice> devices =
        isOpen ? await getConnectionHistory() : networkDevices;
    devices
        .where((d) {
          return d.type ==
              NetWorkType.ethernet.name; //&& d.device.startsWith('en')
        })
        .forEach((device) async {
          if ((isOpen && device.isDisconnected()) ||
              (!isOpen && device.isConnected())) {
            await DeviceCmd.share.setNetworkAutoconnect(
              device.connection,
              isOpen,
            );
            await DeviceCmd.share.connectionNetwork(device.connection, isOpen);
          }
        });
  }

  Future<void> readLicenseCode() async {
    final homeDirectory =
        io.Platform.environment['HOME'] ??
        io.Platform.environment['USERPROFILE'];
    final authDir = "$homeDirectory/.config/surgsmart";
    machineCode = await io.File('$authDir/machine_code').readAsString();
    licenseCode = await io.File('$authDir/license').readAsString();
  }
}

/// APP 遥控器认证令牌信息
class AppControlToken {
  /// 当前令牌, 待生效的
  final current = "".notifier;

  /// 上个令牌, 正在生效的
  String? _last;
  String? get last => _last;

  String get url =>
      "${AppConfig.webBaseUri.origin}/c/?id=${AppContext.share.authorizeInfo.deviceId}&device-token=${current.value}";

  /// 构造函数
  AppControlToken() {
    _last = AppPreferences.controlToken.stringValue;
    refresh();
  }

  /// 刷新遥控器 token
  ///
  /// - [discardLast] 返回是否废弃当前生效令牌
  void refresh({bool Function(String? token)? discardLast}) {
    final random = Random();
    final newToken = List.generate(6, (index) => random.nextInt(10)).join();
    if (newToken == _last || newToken == current.value) {
      refresh(discardLast: discardLast);
    } else {
      if (discardLast?.call(_last) == true) {
        _last = current.value;
        if (_last?.isNotEmpty == true) {
          AppPreferences.controlToken.setString(_last!);
        } else {
          AppPreferences.controlToken.remove();
        }
      }
      current.value = newToken;
    }
  }
}
