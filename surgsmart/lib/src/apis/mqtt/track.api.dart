import 'package:app_foundation/app_foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';

class MqttTrackApi<T extends AppModel> extends MqttApi<T> {
  final int surgeryId;

  @override
  String get topic {
    return "/local/surgsmart/surgeries/${AppContext.share.authorizeInfo.deviceId}/ai/tracking";
  }

  MqttTrackApi.onDoingMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
         apiType: MqttApiType.remote,
         event: MqttApiEvent.onlyListen,
         qos: MqttQos.atMostOnce,
         topic: "",
         type: "ai:tracking",
         body: {},
         model: Graphic() as T,
       );
}
