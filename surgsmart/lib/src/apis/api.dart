import 'dart:async';
import 'dart:convert';

import 'package:app_foundation/app_foundation.dart';
import 'package:dio/dio.dart';
import 'package:meta/meta.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:surgsmart/src/apis/app_config.dart';
import 'package:surgsmart/src/tools/app_context.dart';

class HttpApiDioInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.extra["reqTime"] =
        DateTime.now().dateFormat("YYYY-MM-DD hh:mm:ss.SSS");
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    app.logI({
      "request": {
        "reqTime": response.requestOptions.extra["reqTime"],
        "uri": response.requestOptions.uri.path,
        "method": response.requestOptions.method,
        "headers": response.requestOptions.headers,
        "query": response.requestOptions.queryParameters,
        "data": response.requestOptions.data,
      },
      "response": {
        "realUri": response.realUri.path,
        "headers": response.headers.map,
        "data": response.data,
      }
    });
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    app.logE({
      "request": {
        "reqTime": err.requestOptions.extra["reqTime"],
        "uri": err.requestOptions.uri.path,
        "method": err.requestOptions.method,
        "headers": err.requestOptions.headers,
        "query": err.requestOptions.queryParameters,
        "data": err.requestOptions.data,
      },
      "error": {
        "code": err.response?.statusCode ?? 5000,
        "headers": err.response?.headers.map ?? {},
        "message":
            err.response?.statusMessage ?? err.message ?? "unknown error",
        "data": err.response?.data ?? "NULL",
      }
    });
    handler.next(err);
  }
}

const timeoutDuration = Duration(seconds: 60);

enum HttpApiType {
  surgsmart,
  upgrader;

  static final _clients = [
    Dio(
      BaseOptions(
        baseUrl: AppConfig.apiUrl.origin,
        connectTimeout: timeoutDuration,
        sendTimeout: timeoutDuration,
        receiveTimeout: timeoutDuration,
        contentType: 'application/json',
      ),
    )..interceptors.add(HttpApiDioInterceptor()),
    Dio(
      BaseOptions(
        baseUrl: AppConfig.upgraderUri.origin,
        connectTimeout: timeoutDuration,
        sendTimeout: timeoutDuration,
        receiveTimeout: timeoutDuration,
        contentType: 'application/json',
      ),
    )..interceptors.add(HttpApiDioInterceptor()),
  ];

  Dio get _client => _clients[index];

  void updateHeaders({Map<String, dynamic>? globalHeaders}) {
    if (globalHeaders?.isNotEmpty == true) {
      _client.options.headers.addAll(globalHeaders!);
    }
  }

  void updateBaseUrl(String baseUrl) {
    if (baseUrl.isNotEmpty) {
      _client.options.baseUrl = baseUrl;
    }
  }
}

enum HttpApiMethod { head, get, post, put, delete }

abstract class HttpApi<T extends AppModel> {
  final HttpApiType apiType;

  final HttpApiMethod method;

  final String path;

  final Map<String, dynamic> query;

  final Map<String, dynamic> body;

  final T _model;

  @protected
  HttpApi({
    required this.apiType,
    required this.method,
    required this.path,
    required this.query,
    required this.body,
    required T model,
  }) : _model = model;

  Future<T> request() async {
    try {
      query.removeWhere((key, value) => value == null);
      final response = await apiType._client.request(
        path,
        queryParameters: query,
        data: body,
        options: Options(
          method: method.name.toUpperCase(),
        ),
      );
      if (response.data is List) {
        return _model.extractClone().initWith({"datas": response.data}) as T;
      } else if (response.data is Map) {
        return _model.extractClone().initWith(response.data) as T;
      } else {
        return _model.extractClone().initWith({"data": response.data}) as T;
      }
    } catch (error) {
      AppContext.share.checkNetWorkConnect();
      rethrow;
    }
  }
}

Timer? connectCheckTimer;

enum MqttApiType {
  remote,
  local;

  static final _clients = [
    MqttServerClient.withPort(
      AppConfig.remoteMqttUri.host,
      "device-${AppContext.share.authorizeInfo.deviceId}-com.remote.surgsmart",
      AppConfig.remoteMqttUri.port,
    ),
    MqttServerClient.withPort(
      AppConfig.localeMqttUri.host,
      "device-${AppContext.share.authorizeInfo.deviceId}-com.local.surgsmart",
      AppConfig.localeMqttUri.port,
    ),
  ];

  static final _subscriptions =
      <StreamSubscription<List<MqttReceivedMessage<MqttMessage>>>?>[
    null,
    null,
  ];

  static final _usernames = <String?>[
    "${AppContext.share.authorizeInfo.deviceId}",
    "${AppContext.share.authorizeInfo.deviceId}",
  ];

  static final _passwords = <String?>[null, null];

  static final _apis = <Map<String, Map<String, Set<MqttApi>>>>[{}, {}];

  MqttServerClient get _client => _clients[index];

  StreamSubscription<List<MqttReceivedMessage<MqttMessage>>>?
      get _subscription => _subscriptions[index];

  set _subscription(
          StreamSubscription<List<MqttReceivedMessage<MqttMessage>>>? value) =>
      _subscriptions[index] = value;

  String? get _username => _usernames[index];

  String? get _password => _passwords[index];

  Map<String, Map<String, Set<MqttApi>>> get _mqttApis => _apis[index];

  Future<void> _init() async {
    if (_client.connectionStatus?.state != MqttConnectionState.faulted &&
        _client.keepAlivePeriod != MqttClientConstants.defaultKeepAlive) {
      return;
    }

    /// 设置心跳频率, 默认 0 无心跳
    _client.keepAlivePeriod = 20;

    /// 10 秒内无响应断开连接, 尝试重连
    _client.disconnectOnNoResponsePeriod = 10;

    /// 配置遗嘱消息
    final connMessage = MqttConnectMessage()
        //.startClean() // 设置连接时清除会话
        .withWillTopic(
            '/surgsmart/devices/${AppContext.share.authorizeInfo.deviceId}/status/online') // 设置遗嘱主题
        .withWillMessage('''{
          "identifier": "device-${AppContext.share.authorizeInfo.deviceId}-com.remote.surgsmart",
          "createdAt": ${DateTime.now().millisecondsSinceEpoch},
          "type": "device:status:online",
          "data": {
            "is_online": false
          }
        }''') // 设置遗嘱消息
        .withWillRetain() // 设置遗嘱消息保留
        .withWillQos(MqttQos.atLeastOnce);

    // 3. 设置连接消息
    _client.connectionMessage = connMessage;

    /// 配置 MQTT
    _client
      ..logging(on: false)
      ..autoReconnect = true
      ..resubscribeOnAutoReconnect = true
      ..onConnected = () {
        if (MqttApiType.remote == this) {
          connectCheck();
        }
        app.logI("MQTT $name: connected!");
      }
      ..onAutoReconnect = () {
        if (MqttApiType.remote == this) {
          connectCheck(isDisconnect: true);
        }
        app.logI("MQTT $name: will reconnect!");
      }
      ..onAutoReconnected = () {
        if (MqttApiType.remote == this) {
          connectCheck();
        }
        app.logW("MQTT $name: reconnected!");
      }
      ..onDisconnected = () {
        if (MqttApiType.remote == this) {
          connectCheck(isDisconnect: true);
        }
        app.logW("MQTT $name: disconnected!");
      }
      ..onSubscribed = (topic) {
        app.logI("MQTT $name.$topic: subscribed!");
      }
      ..onSubscribeFail = (topic) {
        app.logE("MQTT $name.$topic: subscribe failed!");
      }
      ..onUnsubscribed = (topic) {
        app.logI("MQTT $name.$topic: unsubscribed!");
      }
      ..pongCallback = () {
        app.logI("MQTT $name.pong OK!");
      };

    /// 建立连接
    try {
      await _client.connect(
        _username,
        _password,
      );
      app.logW("MQTT $name connect access! $_username, $_password)");

      _subscription = _client.updates?.listen(_onData);
    } catch (error) {
      app.logE("MQTT $name connect failed! $error");
      rethrow;
    }
  }

  /// 避免频繁连接断开的抖动，（如存在不可访问的局域网连接）
  void connectCheck({bool isDisconnect = false}) {
    if (isDisconnect) {
      AppContext.share.mqttConnected.value = false;
      return;
    }
    if (connectCheckTimer != null || connectCheckTimer?.isActive == true) {
      return;
    }
    connectCheckTimer?.cancel();
    connectCheckTimer = null;
    connectCheckTimer = Timer(const Duration(seconds: 2), () {
      AppContext.share.mqttConnected.value =
          _client.connectionStatus?.state == MqttConnectionState.connected;
      connectCheckTimer?.cancel();
      connectCheckTimer = null;
    });
  }

  /// 全局对象, 暂不考虑释放问题
  // ignore: unused_element
  void _deInit() {
    _subscription?.cancel();
    _client.disconnect();
  }

  void _onData(List<MqttReceivedMessage<MqttMessage>> messages) {
    try {
      final message = messages.firstOrNull;
      if (message == null) return;
      final topic = message.topic;
      final payload = message.payload as MqttPublishMessage;
      final topicData = jsonDecode(utf8.decode(payload.payload.message));
      final type = topicData["type"] ?? "";
      final typeApis = _mqttApis[topic]?[type] ?? <MqttApi>{};
      final typeData = topicData["data"];
      if (topicData["identifier"] == _client.clientIdentifier) return;
      var isLog = false;
      for (var typeApi in typeApis) {
        if (!isLog && typeApi.enableLog) {
          isLog = true;
          app.logI("""
MQTT $name:
  uri:      ${_client.server}:${_client.port}
  topic:    $topic
  type:     $type
  payload:  $topicData""");
        }
        if (typeData is List) {
          typeApi._onMessage(
            typeApi._model.extractClone().initWith({
              "datas": typeData,
              "token": topicData["token"],
            }),
          );
        } else if (typeData is Map) {
          typeData["token"] = topicData["token"];
          typeApi._onMessage(
            typeApi._model
                .extractClone()
                .initWith(typeData as Map<String, dynamic>),
          );
        } else {
          typeApi._onMessage(
            typeApi._model.extractClone().initWith({
              "data": typeData,
              "token": topicData["token"],
            }),
          );
        }
      }
    } catch (error) {
      app.logE(error.toString());
      rethrow;
    }
  }

  Future<bool> _active(MqttApi mqttApi) async {
    final topicApis = _mqttApis[mqttApi.topic] ?? {};
    _mqttApis[mqttApi.topic] = topicApis;

    final typeApis = topicApis[mqttApi.type] ?? <MqttApi>{};
    topicApis[mqttApi.type] = typeApis;

    typeApis.add(mqttApi);

    try {
      await _init();
      final subscription = _client.subscribe(mqttApi.topic, mqttApi.qos);
      return subscription != null;
    } catch (error) {
      rethrow;
    }
  }

  void _inactive(MqttApi mqttApi) {
    final topicAps = _mqttApis[mqttApi.topic];
    final typeApis = topicAps?[mqttApi.type];
    typeApis?.remove(mqttApi);
    if (typeApis?.isEmpty == true) {
      topicAps?.remove(mqttApi.type);
    }
    if (topicAps?.isEmpty == true) {
      _client.unsubscribe(mqttApi.topic);
    }
  }
}

enum MqttApiEvent {
  onlyListen,
  onlyPublish,
  listenAndPublish,
}

class MqttApiObserver {
  final MqttApi _api;

  const MqttApiObserver(this._api);

  /// 恢复订阅, 以下代码, 用于复用源事件
  /// ```
  /// _api.listen(null)
  /// ```
  void restore() => _api.listen(null);

  /// 取消订阅
  void cancel() => _api._dispose();
}

abstract class MqttApi<T extends AppModel> {
  final MqttApiType apiType;

  final MqttApiEvent event;

  final MqttQos qos;

  final String topic;

  final String type;

  final Map<String, dynamic> body;

  final bool enableLog;

  final T _model;

  /// 消息回调, 类型: void Function(T message)
  /// 因为 dart 范型, 不支持参数类型传递, 导致类型不一致
  /// 因此定义为 dynamic
  dynamic _onMessage;

  var _needListen = true;

  @protected
  MqttApi({
    required this.apiType,
    required this.event,
    required this.qos,
    required this.topic,
    required this.type,
    required this.body,
    required this.enableLog,
    required T model,
  }) : _model = model;

  void publish({bool retain = false}) async {
    assert(
      (MqttApiEvent.onlyListen != event),
      "MQTT $topic/$type disabled to publish!",
    );
    try {
      await apiType._init();
      final builder = MqttClientPayloadBuilder();
      builder.addUTF8String(
        jsonEncode({
          "identifier": apiType._client.clientIdentifier,
          "createdAt": DateTime.now().millisecondsSinceEpoch,
          "type": type,
          "data": body,
        }),
      );
      apiType._client.publishMessage(
        topic,
        qos,
        builder.payload!,
        retain: retain,
      );
    } catch (error) {
      app.logE("MQTT $topic/$type publish failed! $error");
    }
  }

  @useResult
  @mustCallSuper
  MqttApiObserver listen(void Function(T message)? onMessage) {
    assert(
      (MqttApiEvent.onlyPublish != event),
      "MQTT $topic/$type disabled to listen!",
    );
    if (onMessage != null) {
      _onMessage = onMessage;
    } else {
      _needListen = true;
    }
    assert(
      _onMessage != null,
      "MQTT $topic/$type onMessage is null!",
    );
    if (_needListen) {
      apiType._active(this).then((isActive) {
        if (isActive) {
          app.logI("MQTT $topic/$type subscribe success!");
        } else {
          app.logE("MQTT $topic/$type subscribe failed!");
        }
      }).onError((error, stackTrace) {
        if (apiType._client.connectionStatus?.state !=
            MqttConnectionState.connecting) {
          app.logE("MQTT $topic/$type subscribe failed! $error");
        }
        Future.delayed(const Duration(seconds: 1)).then(
          (value) => listen(onMessage),
        );
      });
    }
    return MqttApiObserver(this);
  }

  void _dispose() {
    if (event != MqttApiEvent.onlyPublish) {
      _needListen = false;
      apiType._inactive(this);
    }
  }

  @override
  // ignore: hash_and_equals
  bool operator ==(other) {
    return identical(this, other);
  }
}
