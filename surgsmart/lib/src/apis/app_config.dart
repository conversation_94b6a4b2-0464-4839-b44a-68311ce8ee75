import 'dart:convert';
import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/http/config.api.dart';
import 'package:surgsmart/src/models/service_config_model.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';

class AppConfig {
  static late ServiceConfig config;
  static final String localHost = "127.0.0.1";
  static Uri get apiUrl =>
      AppContext.enablePrivateServer
          ? Uri.parse(AppPreferences.privateServerUrl.stringValue ?? "")
          : getDefaultApiUrl();

  static Uri get upgraderUri => Uri.parse("http://$localHost:5005");
  static Uri get localeMqttUri => Uri.parse("tcp://$localHost:1883");
  static Uri get remoteMqttUri =>
      Uri.parse(config.surgsmartUrls.surgsmartMqttUrlForApp);
  static Uri get webBaseUri => Uri.parse(config.surgsmartUrls.surgsmartWebUrl);
  static Uri get videoBaseUri =>
      Uri.parse(config.surgsmartUrls.surgsmartVideoUrl);

  /// 必须先初始化才可使用相关api
  static Future<bool> initConfig() async {
    try {
      config = await HttpConfigApi<ServiceConfig>.config().request();
      await AppPreferences.appConfig.setString(jsonEncode(config.toMap()));
      return true;
    } catch (e) {
      app.logE("AppConfig获取配置失败: $e");
      String? configJson = AppPreferences.appConfig.stringValue;
      if (configJson != null) {
        config = ServiceConfig().initWith(jsonDecode(configJson));
        return true;
      }
      app.logE("AppConfig本地配置为空: $e");
      return false;
    }
  }

  static Uri getDefaultApiUrl() {
    const mode = String.fromEnvironment('mode', defaultValue: 'development');
    if (mode == 'release') {
      return Uri.parse("https://api.surgsmart.com");
    } else if (mode == 'staging') {
      return Uri.parse("https://api.stage.surgsmart.com");
    } else {
      return Uri.parse("https://api.dev.surgsmart.com");
    }
  }
}


 // const mode = String.fromEnvironment('mode', defaultValue: 'development');
    // final String localHost = "127.0.0.1";
    // switch (mode) {
    //   case 'staging':
    //     webBaseUri = Uri.parse("https://app.stage.surgsmart.com");
    //     remoteMqttUri = Uri.parse('tcp://mqtt.stage.surgsmart.com:1883');
    //     surgsmartUri = Uri.parse('https://api.stage.surgsmart.com');
    //     videoBaseUri = Uri.parse('https://video.stage.withai.com');
    //     break;
    //   // 设置生产环境下的域名
    //   case 'release':
    //     webBaseUri = Uri.parse("https://app.surgsmart.com");
    //     remoteMqttUri = Uri.parse('tcp://mqtt.surgsmart.com:1883');
    //     surgsmartUri = Uri.parse('https://api.surgsmart.com');
    //     videoBaseUri = Uri.parse('https://video.withai.com');
    //     break;
    //   default:
    //     // development/默认使用测试环境调试
    //     webBaseUri = Uri.parse("https://app.dev.surgsmart.com");
    //     remoteMqttUri = Uri.parse('tcp://mqtt.dev.surgsmart.com:1883');
    //     surgsmartUri = Uri.parse('https://api.dev.surgsmart.com');
    //     videoBaseUri = Uri.parse('https://video.dev.withai.com');
    // }
