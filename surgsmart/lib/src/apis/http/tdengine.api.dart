import 'dart:convert';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:dio/dio.dart';
import 'package:isar/isar.dart';
import 'package:surgsmart/src/models/http/report.model.dart';
import 'package:surgsmart/src/models/isar/surgery_features.model.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/db_utils.dart';

class HttpTDEngineApi {
  static final _dio = Dio(
      BaseOptions(
        baseUrl: 'http://127.0.0.1:6041',
        contentType: 'application/json',
      ),
    )
    ..interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // 添加认证头
          options.headers['Authorization'] = _getBasicAuthHeader(
            'root',
            'taosdata',
          );
          return handler.next(options);
        },
      ),
    );

  // 创建 Base64 编码的认证字符串
  static String _getBasicAuthHeader(String username, String password) {
    final credentials = '$username:$password';
    final bytes = utf8.encode(credentials);
    final base64Str = base64.encode(bytes);
    return 'Basic $base64Str';
  }

  /// 生成手术AI信息文件
  static Future<String?> createSurgeryAiInfoFile(
    OfflineSurgeryData surgeryData,
  ) async {
    try {
      String path =
          '${AppContext.share.documentsPath}/v202310/${surgeryData.localSurgeryId}/${surgeryData.remoteSurgeryId}-raw.json';
      File videoAiFile = File(path);
      if (videoAiFile.existsSync()) return path;

      var response = await _dio.post(
        '/rest/sql',
        data: '''
          SELECT * FROM real_time_analyse.analyse_raw WHERE surgery_id=${surgeryData.localSurgeryId} ORDER BY ts ASC
        ''',
      );
      if (response.statusCode == 200) {
        var jsonFields = [
          "body",
          "bleeding",
          "phase",
          "instrument_det",
          "instrument_cls",
          "version",
          "bleeding_point_detection",
        ];
        List<Map<String, dynamic>> aiInfo = fetchAllIntoDict(
          response.data as Map<String, dynamic>,
          jsonFields: jsonFields,
          onlyFirstRawFields: ['version'],
          secondTimestamp: true,
        );

        await videoAiFile.writeAsString(jsonEncode(aiInfo), flush: true);
        return path;
      }
      return null;
    } catch (e) {
      app.logE(e);
      return null;
    }
  }

  static Future<bool> insertToTdEngine(
    int surgeryId,
    String procedureCode,
  ) async {
    final samplePhase = {"lpd": 1310111000, "lc": 1210111000};
    String tableName = "real_time_analyse.analyse";
    String sql = '''
INSERT INTO ${tableName}_${procedureCode}_$surgeryId USING $tableName tags ('$surgeryId', '$procedureCode') VALUES (
        ${DateTime.now().millisecondsSinceEpoch},
        ${samplePhase[procedureCode.toLowerCase()]},
        1110113001,
        '[]',
        '[]',
        '[]',
        1110215000,
        1110291000,
        1110292000,
        1110293000,
        1110294000,
        1110295000
    );
    ''';
    var response = await _dio.post('/rest/sql', data: sql);
    if (response.statusCode == 200) {
      //       String yz = '''SELECT * FROM ${tableName}
      // ORDER BY ts DESC
      // LIMIT 1;''';
      //       var response = await _dio.post(
      //         '/rest/sql',
      //         data: yz,
      //       );
      //       app.logW('offline tdengine insert yz: ${response.data}');
      return true;
    }
    return false;
  }

  static Future<List<Map<String, dynamic>>> surgeryAnalyses(
    int localSurgeryId,
  ) async {
    String tableName = "real_time_analyse.analyse";
    String sql = '''
          SELECT * FROM $tableName WHERE surgery_id=$localSurgeryId ORDER BY ts ASC
        ''';
    var response = await _dio.post('/rest/sql', data: sql);
    List<Map<String, dynamic>> taosValues = [];
    if (response.statusCode == 200) {
      // app.logW(
      //     'tdengine real_time_analyse.analyse query: ${jsonEncode(response.data)}');

      taosValues = fetchAllIntoDict(
        response.data as Map<String, dynamic>,
        jsonFields: ["cvs", "instrument_det", "instrument_cls"],
      );
    }
    return taosValues;
  }

  static Future<List<SurgeryFeatures>> surgeryEventsStatistic(
    int localSurgeryId,
  ) async {
    List<SurgeryFeatures> surgeryFeatures = [];

    SurgeryFeatures feature = SurgeryFeatures();
    feature.localSurgeryId = localSurgeryId;
    feature.featureType = SurgeryFeatureType.surgery.value;
    surgeryFeatures.add(feature);

    List<Map<String, dynamic>> taosValues = await surgeryAnalyses(
      localSurgeryId,
    );
    if (taosValues.isEmpty) return surgeryFeatures;

    feature.duration = flattenTimestamps(taosValues, (v) => v['body']).length;

    List<int> eventIds = [
      1110215001,
      1110292001,
      1110294001,
      1110291001,
      1110295001,
    ];
    List<Map<String, dynamic>> eventsStatistic = [];
    for (var id in eventIds) {
      String? fieldName =
          AppContext.share.insightFullMap[id]?['feature']?['name'];
      if (fieldName == null) {
        app.logW('Event $id not found in insightFullMap');
        continue;
      }
      final events = extractEvent(taosValues, id, fieldName);
      final timeRangeList = (events['time_range_list'] as List<dynamic>?) ?? [];
      final eventDurations =
          timeRangeList
              .map(
                (et) =>
                    (et['end_time_point'] as int) -
                    (et['start_time_point'] as int) +
                    1,
              )
              .toList();
      eventsStatistic.add({
        'event_id': id,
        'count': timeRangeList.length,
        'duration': eventDurations.fold(0, (sum, duration) => sum + duration),
      });
    }
    for (var eventStatistic in eventsStatistic) {
      int count = eventStatistic['count'];
      int duration = eventStatistic['duration'];
      if (count == 0 && duration == 0) {
        continue;
      }
      SurgeryFeatures surgeryFeature = SurgeryFeatures();
      surgeryFeature.localSurgeryId = localSurgeryId;
      surgeryFeature.featureType = SurgeryFeatureType.event.value;
      surgeryFeature.featureId = eventStatistic['event_id'];
      surgeryFeature.count = count;
      surgeryFeature.duration = duration;
      surgeryFeatures.add(surgeryFeature);
    }

    return surgeryFeatures;
  }

  static List<dynamic> flattenTimestamps(
    List<dynamic> taosValues,
    dynamic Function(dynamic) getter, {
    List<dynamic> focusValues = const [],
  }) {
    if (taosValues.isEmpty) {
      return [];
    }

    final DateTime startTs = DateTime.fromMillisecondsSinceEpoch(
      taosValues.first['ts'],
    );
    final DateTime endTs = DateTime.fromMillisecondsSinceEpoch(
      taosValues.last['ts'],
    );
    // 计算总时间差并四舍五入到秒
    final int endSecond = diffSeconds(startTs, endTs);

    final valueMap = <int, dynamic>{};
    // 填充初始值映射
    for (final v in taosValues) {
      final int roundedTs = diffSeconds(
        startTs,
        DateTime.fromMillisecondsSinceEpoch(v['ts']),
      );
      valueMap[roundedTs] = getter(v);
    }
    // 处理强制保留值
    if (focusValues.isNotEmpty) {
      for (final v in taosValues) {
        final dynamic value = getter(v);
        if (focusValues.contains(value)) {
          final int roundedTs = diffSeconds(
            startTs,
            DateTime.fromMillisecondsSinceEpoch(v['ts']),
          );
          valueMap[roundedTs] = value;
        }
      }
    }

    final List<dynamic> flatValues = [];
    for (int ts = 0; ts < endSecond; ts++) {
      if (valueMap.containsKey(ts)) {
        flatValues.add(valueMap[ts]);
      } else {
        // 确保至少有一个值（根据原Python逻辑，第一个值必然存在）
        if (flatValues.isNotEmpty) {
          flatValues.add(flatValues.last);
        } else {
          // 处理极端情况（理论上不会发生）
          flatValues.add(valueMap.values.first);
        }
      }
    }

    return flatValues;
  }

  static int diffSeconds(DateTime startTs, DateTime endTs) {
    final Duration diff = endTs.difference(startTs);
    final double totalSeconds = diff.inMilliseconds / 1000;
    final int roundedTs = totalSeconds.round();
    return roundedTs;
  }

  static Map<String, dynamic> extractBleedingEvent(
    int eventId,
    List<int> bleedingValues,
  ) {
    final List<Map<String, int>> timestamps = [];
    int? bleedingStart;

    for (int idx = 0; idx < bleedingValues.length; idx++) {
      final value = bleedingValues[idx];
      if (value % 10 == 4) {
        // 止血
        if (bleedingStart != null) {
          timestamps.add({
            'start_timestamp': bleedingStart,
            'end_timestamp': idx,
          });
        }
        bleedingStart = null;
      } else if (value % 10 == 3) {
        // 出血
        bleedingStart ??= idx;
      }
    }
    Map<String, dynamic>? bleedingEvents =
        AppContext.share.insightFullMap[eventId]?['feature'];
    if (bleedingEvents == null) {
      return {};
    }
    return {
      ...bleedingEvents,
      'time_range_list':
          timestamps
              .map(
                (ts) => {
                  'start_time_point': ts['start_timestamp'],
                  'end_time_point': ts['end_timestamp'],
                },
              )
              .toList(),
    };
  }

  static List<Map<String, dynamic>> aggregateTimestamps(List<dynamic> values) {
    if (values.isEmpty) {
      return [];
    }

    final List<Map<String, dynamic>> result = [];
    int lastTs = 0;
    int currentIndex = 0;

    while (currentIndex < values.length) {
      final currentId = values[currentIndex];
      int count = 1;

      // 查找连续相同元素的分组
      while (currentIndex + count < values.length &&
          values[currentIndex + count] == currentId) {
        count++;
      }

      // 处理有效ID（非零或非空）
      if (currentId != null && currentId != 0) {
        result.add({
          "id": currentId,
          "start_timestamp": lastTs,
          "end_timestamp": lastTs + count - 1,
        });
      }

      // 更新时间戳基准
      lastTs += count;
      currentIndex += count;
    }

    return result;
  }

  static Map<String, dynamic> extractEvent(
    List<dynamic> taosValues,
    int eventId,
    String fieldName,
  ) {
    // 处理出血升级特殊逻辑
    if (eventId == 1110215001) {
      final flattened = flattenTimestamps(
        taosValues,
        (v) => v[fieldName],
        focusValues: const [1110215004],
      );
      return extractBleedingEvent(eventId, flattened.cast<int>());
    }

    // 通用处理逻辑
    final flattened = flattenTimestamps(taosValues, (v) => v[fieldName]);
    final timestamps = aggregateTimestamps(flattened);

    return {
      ...AppContext.share.insightFullMap[eventId],
      'time_range_list':
          timestamps
              .where((ts) => ts['id'] == eventId)
              .map<Map<String, dynamic>>(
                (ts) => {
                  'start_time_point': ts['start_timestamp'],
                  'end_time_point': ts['end_timestamp'],
                },
              )
              .toList(),
    };
  }

  /// 将查询结果转换为字典列表
  /// jsonFields: 需要转换为json的列
  /// onlyFirstRawFields: 只保留第一行值的列
  static List<Map<String, dynamic>> fetchAllIntoDict(
    Map<String, dynamic> responseData, {
    List<String> jsonFields = const [],
    List<String> onlyFirstRawFields = const [],
    bool secondTimestamp = false, //是否生成秒时间戳（后端对齐采用保留三位小数的浮点秒时间戳）
  }) {
    List<dynamic> columnMeta = responseData['column_meta'];
    List<dynamic> data = responseData['data'];
    List<Map<String, dynamic>> aiInfo = [];
    for (var raw in data) {
      Map<String, dynamic> item = {};
      for (int i = 0; i < raw.length; i++) {
        String key = columnMeta[i][0];
        if (onlyFirstRawFields.contains(key) && aiInfo.isNotEmpty) {
          continue;
        }
        dynamic value;
        if (columnMeta[i][1] == 'TIMESTAMP') {
          DateTime dateTime = DateTime.parse(raw[i]);
          value =
              secondTimestamp
                  ? (dateTime.millisecondsSinceEpoch / 1000)
                  : dateTime.millisecondsSinceEpoch;
        } else if (jsonFields.contains(key)) {
          value = jsonDecode(raw[i]);
        } else {
          value = raw[i];
        }
        item[key] = value;
      }
      aiInfo.add(item);
    }
    return aiInfo;
  }

  static Future<Report> getReportData(OfflineSurgeryData surgery) async {
    List<Map<String, dynamic>> taosValues = await surgeryAnalyses(
      surgery.localSurgeryId,
    );
    if (taosValues.isEmpty) {
      return Report(bodyCavityDuration: 0, surgeryDuration: 0, events: []);
    }

    final List<dynamic> bodyCavities = flattenTimestamps(
      taosValues,
      (v) => v["body"],
    );
    //手术时长，秒
    int surgeryDuration = bodyCavities.length;
    //体腔内时长
    int bodyCavityDuration = bodyCavities.where((b) => b == 1110113000).length;

    //历史手术平均时长,按原python逻辑取最近10000条数据
    // List<OfflineSurgeryData> surgers = await DbUtils()
    //     .isar
    //     .offlineSurgeryDatas
    //     .filter()
    //     .endTimeIsNotNull()
    //     .limit(10000)
    //     .findAll();
    // int totalDuration = surgers.fold(0, (sum, surgery) {
    //   int total = surgery.endTime!.millisecondsSinceEpoch -
    //       surgery.createTime.millisecondsSinceEpoch;
    //   return sum + (total > 0 ? total : 0);
    // });
    // double avgDuration = totalDuration / 1000 / surgers.length;

    //获取该手术分析数据
    List<SurgeryFeatures> surgeryEvents =
        await DbUtils().isar.surgeryFeatures
            .filter()
            .localSurgeryIdEqualTo(surgery.localSurgeryId)
            .featureTypeEqualTo(SurgeryFeatureType.event.value)
            .featureIdIsNotNull()
            .findAll();

    Map<int, SurgeryFeatures> eventsMap = {
      for (var event in surgeryEvents) event.featureId!: event,
    };

    List<OfflineSurgeryData> userProcedureSurgers =
        await DbUtils().isar.offlineSurgeryDatas
            .filter()
            .endTimeIsNotNull()
            .surgeonIdEqualTo(surgery.surgeonId)
            .procedureIdEqualTo(surgery.procedureId)
            .findAll();

    List<int> userProcedureSurgerIds =
        userProcedureSurgers.map((e) => e.localSurgeryId).toList();

    /// 获取该手术的历史数据统计
    final surgeryFeatures =
        await DbUtils().isar.surgeryFeatures
            .filter()
            .featureIdIsNotNull()
            .anyOf(
              userProcedureSurgerIds,
              (q, int localSurgeryId) =>
                  q.localSurgeryIdEqualTo(localSurgeryId),
            )
            .featureTypeEqualTo(SurgeryFeatureType.event.value)
            .findAll();

    /// 根据featureId进行分组聚合
    Map<int, Map<String, int>> featureIdAggregation = {};
    for (var feature in surgeryFeatures) {
      Map<String, int>? statistics = featureIdAggregation[feature.featureId!];
      featureIdAggregation[feature.featureId!] = {
        "count": feature.count + (statistics?["count"] ?? 0),
        "duration": feature.duration + (statistics?["duration"] ?? 0),
      };
    }

    List<int> eventIds = [1110215001, 1110292001, 1110291001];
    if (surgery.readProcedure()?.algorithmCode.toUpperCase() == "LC") {
      //剪刀离断
      eventIds.add(1110295001);
    } else {
      //切割闭合
      eventIds.add(1110294001);
    }
    final eventAttrInfos = AppContext.share.events;
    List<SurgeryEventInfo> eventListInfo =
        eventIds
            .map(
              (id) => SurgeryEventInfo().initWith({
                'value': id,
                'label': eventAttrInfos[id]?.label ?? '',
                'name': eventAttrInfos[id]?.name ?? '',
                'this_surgery_count': eventsMap[id]?.count ?? 0,
                'this_surgery_duration': eventsMap[id]?.duration ?? 0,
                'overall_average_count': double.parse(
                  (featureIdAggregation[id]?['count'] ??
                          0 / userProcedureSurgerIds.length)
                      .toStringAsFixed(2),
                ),
                'overall_average_duration': double.parse(
                  (featureIdAggregation[id]?['duration'] ??
                          0 / userProcedureSurgerIds.length)
                      .toStringAsFixed(2),
                ),
              }),
            )
            .toList();

    return Report(
      bodyCavityDuration: bodyCavityDuration,
      surgeryDuration: surgeryDuration,
      events: eventListInfo,
    );
  }
}
