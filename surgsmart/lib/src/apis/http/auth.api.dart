import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';

class HttpAuthApi<T extends AppModel> extends HttpApi<T> {
  /// 设备授权
  HttpAuthApi.authorize({
    required String machineCode,
    required String licenseCode,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/device/authorize/",
          query: {},
          body: {"machine_code": machineCode, "license": licenseCode},
          model: AuthorizeInfo() as T,
        );

  /// 视频库认证
  HttpAuthApi.videoAclAuth()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/video-library/acl/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

  /// 获取授权码
  HttpAuthApi.getLicenseCode({
    required String machineCode,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/device/",
          query: {},
          body: {"machine_code": machineCode},
          model: ApiModel() as T,
        );
}
