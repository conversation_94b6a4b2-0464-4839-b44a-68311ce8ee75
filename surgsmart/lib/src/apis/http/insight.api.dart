import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/file_statistics.dart';
import 'package:surgsmart/src/models/http/report.model.dart';

class HttpInsightApi<T extends AppModel> extends HttpApi<T> {
  /// 手术统计
  HttpInsightApi.surgeryStatistic()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/insight/surgery/statistic/",
          query: {},
          body: {},
          model: SurgeryStatisticInfo() as T,
        );

  /// 手术时长
  HttpInsightApi.surgeryDuration({required int surgeryId})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/insight/surgery/$surgeryId/duration/",
          query: {},
          body: {},
          model: SurgeryDurationInfo() as T,
        );

  /// 手术事件
  HttpInsightApi.surgeryEvents({required int surgeryId})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/local/surgery/$surgeryId/event-count-and-durations/",
          query: {},
          body: {},
          model: SurgeryEventListInfo() as T,
        );

  /// 手术体腔内时长
  HttpInsightApi.surgeryInBodyDuration({required int surgeryId})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/insight/surgery/$surgeryId/bodycavity/duration/",
          query: {},
          body: {},
          model: SurgeryInBodyDurationInfo() as T,
        );

  /// 通知报告生成
  HttpInsightApi.uploadRawCallback(
      {required int surgeryId, required String aiCosKey})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/insight/surgery-analysis/$surgeryId/upload-raw-callback/",
          query: {},
          body: {"key": aiCosKey},
          model: ApiModel() as T,
        );
}
