import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/service_config_model.dart';

class HttpConfigApi<T extends AppModel> extends HttpApi<T> {
  /// 通过Api获取配置信息
  HttpConfigApi.config()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/config/surgsmart/",
          query: {},
          body: {},
          model: ServiceConfig() as T,
        );
}
