import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/file.model.dart';

class HttpLocalApi<T extends AppModel> extends HttpApi<T> {
  /// 开启算法
  ///
  /// - [surgeryId] 手术ID
  ///
  /// - [procedureCode] 术式代码
  HttpLocalApi.startAi(
      {required int surgeryId,
      required String procedureCode,
      required String videoDevicePath})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/local/startai/${surgeryId}_${procedureCode.toUpperCase()}/",
          query: {"video_device_path": videoDevicePath},
          body: {},
          model: ApiModel() as T,
        );

  /// 停止算法
  ///
  /// - [surgeryId] 手术ID
  ///
  /// - [procedureCode] 术式代码
  HttpLocalApi.stopAi({
    required int surgeryId,
    required String procedureCode,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/local/stopai/${surgeryId}_${procedureCode.toUpperCase()}/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

  /// 手术文件信息分页数据
  ///
  /// - [admissionNumber] 患者住院号, 缺省值空
  ///
  /// - [userId] 用户ID, 缺省值空
  ///
  /// - [procedureName] 手术名称, 缺省值空
  ///
  /// - [startTime] 开始时间, 缺省值空
  ///
  /// - [endTime] 结束时间, 缺省值空
  ///
  /// - [page] 页码, 缺省值 1
  ///
  /// - [pageSize] 每页数量, 缺省值 4
  HttpLocalApi.surgeryFiles({
    String? admissionNumber,
    int? userId,
    String? procedureName,
    int? startTime,
    int? endTime,
    int page = 1,
    int pageSize = 4,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/local/surgery-file/",
          query: {
            'admission_number__contains': admissionNumber,
            'user_id': userId,
            'procedure_name': procedureName,
            'surgery_time__gte': startTime,
            'surgery_time__lte': endTime,
            'page': page,
            'page_size': pageSize,
          },
          body: {},
          model: SurgeryFileListInfo() as T,
        );

  /// 所有手术文件状态
  ///
  /// - [admissionNumber] 患者住院号, 缺省值空
  ///
  /// - [userId] 用户ID, 缺省值空
  ///
  /// - [procedureName] 手术名称, 缺省值空
  ///
  /// - [startTime] 开始时间, 缺省值空
  ///
  /// - [endTime] 结束时间, 缺省值空
  HttpLocalApi.surgeryFileStatus({
    String? admissionNumber,
    int? userId,
    String? procedureName,
    int? startTime,
    int? endTime,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/local/surgery-file/all/",
          query: {
            'admission_number__contains': admissionNumber,
            'user_id': userId,
            'procedure_name': procedureName,
            'surgery_time__gte': startTime,
            'surgery_time__lte': endTime,
          },
          body: {},
          model: SurgeryFileStatusListInfo() as T,
        );

  HttpLocalApi.surgeryFileAttributes({required List<int> surgeryIds})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/local/surgery-file/get-details/",
          query: {},
          body: {'surgery_ids': surgeryIds},
          model: SurgeryFileAttributeListInfo() as T,
        );

  /// 删除手术
  ///
  /// - [surgeryIds] 手术ID列表
  HttpLocalApi.deleteSurgeryFile({required List<int> surgeryIds})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.delete,
          path: "/local/surgery-file/",
          query: {},
          body: {'surgery_ids': surgeryIds},
          model: ApiModel() as T,
        );
}
