import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/file_state.mode.dart';

class HttpUpLoadApi<T extends AppModel> extends HttpApi<T> {
  /// 开始上传
  HttpUpLoadApi.start()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/local/file_upload/start/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

  /// 检查是否还有未上传
  HttpUpLoadApi.check()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/local/file_upload/",
          query: {},
          body: {},
          model: FileStatus() as T,
        );

  /// 停止上传
  HttpUpLoadApi.stop()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/local/file_upload/stop/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

}
