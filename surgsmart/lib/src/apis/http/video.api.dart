import 'dart:convert';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:dio/dio.dart';
import 'package:surgsmart/src/apis/app_config.dart';
import 'package:surgsmart/src/apis/http/auth.api.dart';
import 'package:surgsmart/src/apis/http/surgery.api.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';

class HttpVideoApi {
  static final _dio = Dio(
    BaseOptions(
      baseUrl: AppConfig.videoBaseUri.toString(),
      contentType: 'application/json',
    ),
  );

  // 创建 Base64 编码的认证字符串
  static String _getBasicAuthHeader(String username, String password) {
    final credentials = '$username:$password';
    final bytes = utf8.encode(credentials);
    final base64Str = base64.encode(bytes);
    return 'Basic $base64Str';
  }

  ///
  static Future<dynamic> getOrCreateVideo({
    required int surgeryId,
    required String videoKey,
  }) async {
    Response<dynamic> res = await _dio.get('/video/$videoKey/');

    await HttpSurgeryApi.putVideoKey(
      surgeryId: surgeryId,
      videoKey: videoKey,
    ).request();
    return res.data;
  }

  static Future<dynamic> getOrAddSegment({
    required List<dynamic> segments,
    required String filePath,
    required String videoKey,
    required int order,
  }) async {
    String segmentName = filePath.substring(filePath.lastIndexOf('/') + 1);
    for (var segment in segments) {
      if (segment["name"] == segmentName) return segment;
    }
    FileStat fileStat = await File(filePath).stat();
    final segment = {
      "name": segmentName,
      "size": fileStat.size,
      "order": order,
    };
    Response<dynamic> res = await _dio.post(
      "/video/$videoKey/segment/",
      data: segment,
    );
    return res.data;
  }

  static Future<dynamic> getStorageCredentials() async {
    Response<dynamic> res = await _dio.post("/storage-credential/");
    return res.data;
  }

  static Future<dynamic> setSegmentStatus({
    required String videoKey,
    required int segmentId,
    required String status,
    String? failedReason,
  }) async {
    Response<dynamic> res = await _dio.put(
      '/video/$videoKey/segment/$segmentId/',
      data: {"status": status, "failed_reason": failedReason},
    );

    return res.data;
  }

  static Future<void> videoAclAuth() async {
    //获取视频库认证信息
    ApiModel apiModel = await HttpAuthApi<ApiModel>.videoAclAuth().request();
    String videoAcAuth = _getBasicAuthHeader(
      apiModel.map?["app_id"],
      apiModel.map?["secret_key"],
    );
    app.logW('videoSync-----videoAclAuth: ${apiModel.map.toString()}');
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          options.headers['Authorization'] = videoAcAuth;
          return handler.next(options);
        },
      ),
    );
  }

  static Future<String?> getVideoKey({required int surgeryId}) async {
    await videoAclAuth();
    //查询手术信息video_key
    SurgeryInfo? surgeryInfo;
    try {
      surgeryInfo =
          await HttpSurgeryApi<SurgeryInfo>.info(
            surgeryId: surgeryId,
          ).request();
    } catch (e) {
      app.logE('get SurgeryInfo error: $e');
      if (e.toString().contains("404")) {
        return null;
      }
    }
    String videoKey = surgeryInfo!.videoKey ?? "";
    if (videoKey.isEmpty) {
      final tags = [
        "user-${surgeryInfo.user?.id}",
        "procedure-${surgeryInfo.procedure?.id}",
        "surgery-${surgeryInfo.id}",
        "device-${AppContext.share.authorizeInfo.deviceId}",
      ];

      Response<dynamic> registerRes = await _dio.post(
        '/video/',
        data: {"tags": tags, "segments": [], "allow_merge": false},
      );
      videoKey = registerRes.data['video_key'];
    }
    return videoKey;
  }

  static Future<dynamic> allowMerge({required String videoKey}) async {
    Response<dynamic> res = await _dio.put(
      '/video/$videoKey/',
      data: {"allow_merge": true},
    );
    return res.data;
  }
}
