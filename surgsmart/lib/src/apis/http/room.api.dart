import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/room.model.dart';

class HttpRoomApi<T extends AppModel> extends HttpApi<T> {
  /// 会诊授权
  ///
  /// - [deviceId] 设备ID
  /// - [isEndoscope] 是否是腔镜
  /// - [randomUid] 是否随机生成uid
  HttpRoomApi.authorize({
    required int deviceId,
    bool isEndoscope = true,
    bool randomUid = false,
  }) : super(
         apiType: HttpApiType.surgsmart,
         method: HttpApiMethod.post,
         path: "/room/$deviceId/consultation/",
         query: {
           "client_type": isEndoscope ? "1" : "2",
           "use_random_uid": randomUid ? "1" : "0",
         }, //client_type: 1=腔镜，2=外景
         body: {},
         model: RoomAuthorizeInfo() as T,
       );

  /// 会诊开始
  ///
  /// - [surgeryId] 手术ID
  HttpRoomApi.start({required int surgeryId, required int localSurgeryId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.post,
        path: "/room/",
        query: {},
        body: {"surgery_id": surgeryId, 'local_surgery_id': localSurgeryId},
        model: ApiModel() as T,
      );

  /// 会诊结束
  ///
  /// - [surgeryId] 手术ID
  HttpRoomApi.stop({required int deviceId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.delete,
        path: "/room/$deviceId/",
        query: {},
        body: {},
        model: ApiModel() as T,
      );

  /// 获取会诊用户列表
  ///
  /// - [deviceId] 设备ID
  HttpRoomApi.users({required int deviceId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.get,
        path: "/room/$deviceId/all_user/",
        query: {},
        body: {},
        model: DoctorListInfo() as T,
      );

  /// 分享会诊
  ///
  /// - [deviceId] 设备ID
  ///
  /// - [expiresInDay] 分享有效期（天）, 0 表示永久有效
  HttpRoomApi.share({required int deviceId, int expiresInDay = 0})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.post,
        path: "/room/$deviceId/share/",
        query: {},
        body: {"expires_in_day": 0},
        model: RoomShareInfo() as T,
      );

  /// 获取可邀请列表
  ///
  /// - [deviceId] 设备ID
  ///
  /// - [orgId] 组织ID
  HttpRoomApi.getInvitationList({required int deviceId, required int orgId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.get,
        path: "/room/$deviceId/invitation/user/",
        query: {"org_id": orgId},
        body: {},
        model: InvitationInfo() as T,
      );

  /// 邀请用户加入协同
  ///
  /// - [deviceId] 设备ID
  ///
  /// - [userId] 用户ID
  HttpRoomApi.invitationUser({required int deviceId, required int userId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.post,
        path: "/room/$deviceId/invitation/user/$userId/",
        query: {},
        body: {},
        model: ApiModel() as T,
      );
}
