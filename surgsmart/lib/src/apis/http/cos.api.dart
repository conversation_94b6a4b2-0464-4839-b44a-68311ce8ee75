import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import '../../models/http/cos.model.dart';

class HttpCosApi<T extends AppModel> extends HttpApi<T> {
  /// 获取COS授权信息
  /// - [deviceId] 设备ID
  ///
  /// - [storageType] 存储类型 video-frame/mark
  HttpCosApi.getCosCredential({required String storageType})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/cos/upload-credential/",
          query: {},
          body: {"storage_type": storageType},
          model: CosCredential() as T,
        );

  /// 获取COS预签名地址
  HttpCosApi.getCosPreSignUrl({
    required String path,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/cos/presign-url/",
          query: {},
          body: {"resource_key": path},
          model: CosPreSignUrl() as T,
        );

  /// 获取术中截图上传COS授权信息
  /// - [deviceId] 设备ID
  ///
  /// - [storageType] 存储类型
  HttpCosApi.getScreenshotCosCredential({
    required int deviceId,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/room/$deviceId/upload_credential/",
          query: {},
          body: {},
          model: CosCredential() as T,
        );

  /// 获取手术ai数据上传COS授权信息
  /// - [deviceId] 设备ID
  ///
  /// - [storageType] 存储类型
  HttpCosApi.getAiInfoCosCredential()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/insight/surgery-analysis/upload-credential/",
          query: {},
          body: {},
          model: CosCredential() as T,
        );
}
