import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/live.model.dart';

class HttpLiveApi<T extends AppModel> extends HttpApi<T> {
  /// 开始直播
  ///
  /// - [surgeryId] 手术ID
  ///
  /// - [title] 直播标题
  HttpLiveApi.start({
    required int surgeryId,
    required String title,
    String description = "",
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/live/",
          query: {},
          body: {
            "title": title,
            "description": description,
            "surgery_id": surgeryId
          },
          model: StartLiveInfo() as T,
        );

  /// 停止直播
  /// 原/live/surgeryId/stop/经过服务转发，现直接访问
  /// - [surgeryId] 手术ID
  HttpLiveApi.stop({required int deviceId})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.delete,
          path: "/live/$deviceId/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

  /// 直播人数
  ///
  /// - [deviceId] 设备ID
  HttpLiveApi.userCount({required int deviceId})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/live/$deviceId/user-count/",
          query: {},
          body: {},
          model: LiveInfo() as T,
        );

  /// 分享直播
  ///
  /// - [deviceId] 设备ID
  ///
  /// - [expiresInDay] 分享有效期（天）, 0 表示永久有效
  HttpLiveApi.share({
    required int deviceId,
    int expiresInDay = 0,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/live/$deviceId/share/",
          query: {},
          body: {'expires_in_day': expiresInDay},
          model: LiveShareInfo() as T,
        );
}
