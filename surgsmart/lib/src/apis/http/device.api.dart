import 'dart:convert';

import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/org.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/models/http/setting.model.dart';

class HttpDeviceApi<T extends AppModel> extends HttpApi<T> {
  /// 设备组织列表
  HttpDeviceApi.currentOrg()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/device/current/org/",
          query: {"org_type": "department"},
          body: {},
          model: OrgListInfo() as T,
        );

  /// 设备组织主刀列表
  HttpDeviceApi.currentOrgSurgeon()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/device/current/org-surgeon/",
          query: {},
          body: {},
          model: OrgSurgeonListInfo() as T,
        );

  /// 设备组织术式列表
  HttpDeviceApi.currentOrgProcedure({int? organizationId})
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/device/current/org-procedure/",
          query: organizationId == null
              ? {}
              : {"medical_organization_id": organizationId},
          body: {},
          model: OrgProcedureListInfo() as T,
        );

  /// 设备信息上报
  HttpDeviceApi.currentInfo(String ipInfo)
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.put,
          path: "/device/current/info/",
          query: {},
          body: {"ip_addr_output": jsonDecode(ipInfo)},
          model: ApiModel() as T,
        );

  /// 设备设置信息
  HttpDeviceApi.settingInfo()
      : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.get,
          path: "/device/setting/",
          query: {},
          body: {},
          model: SettingInfo() as T,
        );
}
