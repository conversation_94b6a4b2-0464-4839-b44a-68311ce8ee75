// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'surgery_features.model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetSurgeryFeaturesCollection on Isar {
  IsarCollection<SurgeryFeatures> get surgeryFeatures => this.collection();
}

const SurgeryFeaturesSchema = CollectionSchema(
  name: r'SurgeryFeatures',
  id: 2259067619979011434,
  properties: {
    r'count': PropertySchema(
      id: 0,
      name: r'count',
      type: IsarType.long,
    ),
    r'duration': PropertySchema(
      id: 1,
      name: r'duration',
      type: IsarType.long,
    ),
    r'featureId': PropertySchema(
      id: 2,
      name: r'featureId',
      type: IsarType.long,
    ),
    r'featureType': PropertySchema(
      id: 3,
      name: r'featureType',
      type: IsarType.long,
    ),
    r'localSurgeryId': PropertySchema(
      id: 4,
      name: r'localSurgeryId',
      type: IsarType.long,
    )
  },
  estimateSize: _surgeryFeaturesEstimateSize,
  serialize: _surgeryFeaturesSerialize,
  deserialize: _surgeryFeaturesDeserialize,
  deserializeProp: _surgeryFeaturesDeserializeProp,
  idName: r'id',
  indexes: {
    r'localSurgeryId': IndexSchema(
      id: -266070205605254372,
      name: r'localSurgeryId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'localSurgeryId',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _surgeryFeaturesGetId,
  getLinks: _surgeryFeaturesGetLinks,
  attach: _surgeryFeaturesAttach,
  version: '3.1.0+1',
);

int _surgeryFeaturesEstimateSize(
  SurgeryFeatures object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _surgeryFeaturesSerialize(
  SurgeryFeatures object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.count);
  writer.writeLong(offsets[1], object.duration);
  writer.writeLong(offsets[2], object.featureId);
  writer.writeLong(offsets[3], object.featureType);
  writer.writeLong(offsets[4], object.localSurgeryId);
}

SurgeryFeatures _surgeryFeaturesDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = SurgeryFeatures();
  object.count = reader.readLong(offsets[0]);
  object.duration = reader.readLong(offsets[1]);
  object.featureId = reader.readLongOrNull(offsets[2]);
  object.featureType = reader.readLong(offsets[3]);
  object.id = id;
  object.localSurgeryId = reader.readLong(offsets[4]);
  return object;
}

P _surgeryFeaturesDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLong(offset)) as P;
    case 1:
      return (reader.readLong(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    case 3:
      return (reader.readLong(offset)) as P;
    case 4:
      return (reader.readLong(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _surgeryFeaturesGetId(SurgeryFeatures object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _surgeryFeaturesGetLinks(SurgeryFeatures object) {
  return [];
}

void _surgeryFeaturesAttach(
    IsarCollection<dynamic> col, Id id, SurgeryFeatures object) {
  object.id = id;
}

extension SurgeryFeaturesQueryWhereSort
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QWhere> {
  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhere>
      anyLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'localSurgeryId'),
      );
    });
  }
}

extension SurgeryFeaturesQueryWhere
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QWhereClause> {
  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause>
      localSurgeryIdEqualTo(int localSurgeryId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'localSurgeryId',
        value: [localSurgeryId],
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause>
      localSurgeryIdNotEqualTo(int localSurgeryId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [],
              upper: [localSurgeryId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [localSurgeryId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [localSurgeryId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [],
              upper: [localSurgeryId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause>
      localSurgeryIdGreaterThan(
    int localSurgeryId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'localSurgeryId',
        lower: [localSurgeryId],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause>
      localSurgeryIdLessThan(
    int localSurgeryId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'localSurgeryId',
        lower: [],
        upper: [localSurgeryId],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterWhereClause>
      localSurgeryIdBetween(
    int lowerLocalSurgeryId,
    int upperLocalSurgeryId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'localSurgeryId',
        lower: [lowerLocalSurgeryId],
        includeLower: includeLower,
        upper: [upperLocalSurgeryId],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension SurgeryFeaturesQueryFilter
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QFilterCondition> {
  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      countEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'count',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      countGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'count',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      countLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'count',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      countBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'count',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      durationEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'duration',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      durationGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'duration',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      durationLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'duration',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      durationBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'duration',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'featureId',
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'featureId',
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'featureId',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'featureId',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'featureId',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'featureId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureTypeEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'featureType',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureTypeGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'featureType',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureTypeLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'featureType',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      featureTypeBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'featureType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      localSurgeryIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      localSurgeryIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      localSurgeryIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterFilterCondition>
      localSurgeryIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localSurgeryId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension SurgeryFeaturesQueryObject
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QFilterCondition> {}

extension SurgeryFeaturesQueryLinks
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QFilterCondition> {}

extension SurgeryFeaturesQuerySortBy
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QSortBy> {
  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy> sortByCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'count', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'count', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByDurationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByFeatureId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureId', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByFeatureIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureId', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByFeatureType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureType', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByFeatureTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureType', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      sortByLocalSurgeryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.desc);
    });
  }
}

extension SurgeryFeaturesQuerySortThenBy
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QSortThenBy> {
  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy> thenByCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'count', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'count', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByDurationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByFeatureId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureId', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByFeatureIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureId', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByFeatureType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureType', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByFeatureTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'featureType', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.asc);
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QAfterSortBy>
      thenByLocalSurgeryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.desc);
    });
  }
}

extension SurgeryFeaturesQueryWhereDistinct
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QDistinct> {
  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QDistinct> distinctByCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'count');
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QDistinct>
      distinctByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'duration');
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QDistinct>
      distinctByFeatureId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'featureId');
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QDistinct>
      distinctByFeatureType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'featureType');
    });
  }

  QueryBuilder<SurgeryFeatures, SurgeryFeatures, QDistinct>
      distinctByLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localSurgeryId');
    });
  }
}

extension SurgeryFeaturesQueryProperty
    on QueryBuilder<SurgeryFeatures, SurgeryFeatures, QQueryProperty> {
  QueryBuilder<SurgeryFeatures, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<SurgeryFeatures, int, QQueryOperations> countProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'count');
    });
  }

  QueryBuilder<SurgeryFeatures, int, QQueryOperations> durationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'duration');
    });
  }

  QueryBuilder<SurgeryFeatures, int?, QQueryOperations> featureIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'featureId');
    });
  }

  QueryBuilder<SurgeryFeatures, int, QQueryOperations> featureTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'featureType');
    });
  }

  QueryBuilder<SurgeryFeatures, int, QQueryOperations>
      localSurgeryIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localSurgeryId');
    });
  }
}
