import 'dart:core';
import 'package:isar/isar.dart';
part 'surgery_features.model.g.dart';

enum SurgeryFeatureType {
  surgery(1),
  event(4);

  final int value;
  const SurgeryFeatureType(this.value);
}

@collection
class SurgeryFeatures {
  Id id = Isar.autoIncrement;
  @Index(type: IndexType.value)
  late int localSurgeryId; //本地手术id
  late int featureType; //特征类型
  int? featureId; //特征id
  int count = 0; //数量
  int duration = 0; //时长

  @override
  String toString() {
    return '''
    id: $id
    localSurgeryId: $localSurgeryId
    featureType: $featureType
    featureId: $featureId
    count: $count
    duration: $duration
    ''';
  }
}
