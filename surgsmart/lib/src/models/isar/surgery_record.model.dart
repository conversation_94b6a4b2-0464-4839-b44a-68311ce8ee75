import 'dart:convert';
import 'dart:core';
import 'dart:io';
import 'package:isar/isar.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/org.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
part 'surgery_record.model.g.dart';

@collection
class OfflineSurgeryData {
  @ignore
  int errCount = 0;
  @ignore
  bool videoSyncing = false;

  Id id = Isar.autoIncrement;
  @Index(type: IndexType.value)
  late int localSurgeryId; //本地手术id
  late int orgId; //组织id
  late int surgeonId; //主刀id
  late int procedureId; //术式id
  late String resolution; //分辨率
  String? admissionNumber; //住院号
  late DateTime createTime; //创建时间
  DateTime? endTime; //结束时间
  int? remoteSurgeryId; //远程手术id
  bool notifyReportCreated = false; //是否通知生成报告
  bool videoSynced = false; //手术视频是否同步
  bool? markUploaded; //睿标记是否上传
  bool localVideoMerged = false; //本地手术视频是否合并
  int recordStatus = 0; //记录状态 0:正常 1:已删除

  @override
  String toString() {
    return '''
    id: $id
    localSurgeryId: $localSurgeryId
    orgId: $orgId
    surgeonId: $surgeonId
    procedureId: $procedureId
    admissionNumber: $admissionNumber
    resolution: $resolution
    createTime: $createTime
    endTime: $endTime
    markUploaded: $markUploaded
    remoteSurgeryId: $remoteSurgeryId
    notifyReportCreated: $notifyReportCreated
    videoSynced: $videoSynced
    localVideoMerged: $localVideoMerged
    recordStatus: $recordStatus
    ''';
  }

  OrgInfo? readDepartment({int? customId}) {
    OrgListInfo departmentInfo = OrgListInfo().initWith(
      jsonDecode(AppPreferences.orgInfo.stringValue ?? '{}'),
    );
    Map<int, OrgInfo> departmentMap = Map.fromEntries(
      departmentInfo.datas.map((e) => MapEntry(e.id, e)),
    );
    return departmentMap[customId ?? orgId];
  }

  OrgProcedureInfo? readProcedure({int? customId}) {
    OrgProcedureListInfo procedureInfo = OrgProcedureListInfo().initWith(
      jsonDecode(AppPreferences.procedureInfo.stringValue ?? '{}'),
    );
    Map<int, OrgProcedureInfo> procedureMap = Map.fromEntries(
      procedureInfo.datas.map((e) => MapEntry(e.id, e)),
    );
    return procedureMap[customId ?? procedureId];
  }

  OrgSurgeonInfo? readSurgeon({int? customId}) {
    OrgSurgeonListInfo surgeonInfo = OrgSurgeonListInfo().initWith(
      jsonDecode(AppPreferences.surgeonInfo.stringValue ?? '{}'),
    );
    Map<int, OrgSurgeonInfo> surgeonMap = Map.fromEntries(
      surgeonInfo.datas.map((e) => MapEntry(e.id, e)),
    );
    return surgeonMap[customId ?? surgeonId];
  }

  String getVideoPath() {
    return '${AppContext.share.documentsPath}/v202310/merged/$localSurgeryId.mp4';
  }

  String getRecordedDir() {
    return '${AppContext.share.documentsPath}/v202310/$localSurgeryId/recorded/';
  }

  String getSurgeryDir() {
    return '${AppContext.share.documentsPath}/v202310/$localSurgeryId/';
  }

  bool isNoSignalAll() {
    return File('${getRecordedDir()}/noSignalAll').existsSync();
  }

  Future<int?> getVideoSize({String? path}) async {
    File file = File(path ?? getVideoPath());
    if (!await file.exists()) {
      return null;
    }
    FileStat fileStat = await file.stat();
    return fileStat.size;
  }
}
