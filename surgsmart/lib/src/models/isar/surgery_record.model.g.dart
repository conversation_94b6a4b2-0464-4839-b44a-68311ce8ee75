// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'surgery_record.model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetOfflineSurgeryDataCollection on Isar {
  IsarCollection<OfflineSurgeryData> get offlineSurgeryDatas =>
      this.collection();
}

const OfflineSurgeryDataSchema = CollectionSchema(
  name: r'OfflineSurgeryData',
  id: 6244461147213423536,
  properties: {
    r'admissionNumber': PropertySchema(
      id: 0,
      name: r'admissionNumber',
      type: IsarType.string,
    ),
    r'createTime': PropertySchema(
      id: 1,
      name: r'createTime',
      type: IsarType.dateTime,
    ),
    r'endTime': PropertySchema(
      id: 2,
      name: r'endTime',
      type: IsarType.dateTime,
    ),
    r'localSurgeryId': PropertySchema(
      id: 3,
      name: r'localSurgeryId',
      type: IsarType.long,
    ),
    r'localVideoMerged': PropertySchema(
      id: 4,
      name: r'localVideoMerged',
      type: IsarType.bool,
    ),
    r'markUploaded': PropertySchema(
      id: 5,
      name: r'markUploaded',
      type: IsarType.bool,
    ),
    r'notifyReportCreated': PropertySchema(
      id: 6,
      name: r'notifyReportCreated',
      type: IsarType.bool,
    ),
    r'orgId': PropertySchema(
      id: 7,
      name: r'orgId',
      type: IsarType.long,
    ),
    r'procedureId': PropertySchema(
      id: 8,
      name: r'procedureId',
      type: IsarType.long,
    ),
    r'recordStatus': PropertySchema(
      id: 9,
      name: r'recordStatus',
      type: IsarType.long,
    ),
    r'remoteSurgeryId': PropertySchema(
      id: 10,
      name: r'remoteSurgeryId',
      type: IsarType.long,
    ),
    r'resolution': PropertySchema(
      id: 11,
      name: r'resolution',
      type: IsarType.string,
    ),
    r'surgeonId': PropertySchema(
      id: 12,
      name: r'surgeonId',
      type: IsarType.long,
    ),
    r'videoSynced': PropertySchema(
      id: 13,
      name: r'videoSynced',
      type: IsarType.bool,
    )
  },
  estimateSize: _offlineSurgeryDataEstimateSize,
  serialize: _offlineSurgeryDataSerialize,
  deserialize: _offlineSurgeryDataDeserialize,
  deserializeProp: _offlineSurgeryDataDeserializeProp,
  idName: r'id',
  indexes: {
    r'localSurgeryId': IndexSchema(
      id: -266070205605254372,
      name: r'localSurgeryId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'localSurgeryId',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _offlineSurgeryDataGetId,
  getLinks: _offlineSurgeryDataGetLinks,
  attach: _offlineSurgeryDataAttach,
  version: '3.1.0+1',
);

int _offlineSurgeryDataEstimateSize(
  OfflineSurgeryData object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.admissionNumber;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.resolution.length * 3;
  return bytesCount;
}

void _offlineSurgeryDataSerialize(
  OfflineSurgeryData object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.admissionNumber);
  writer.writeDateTime(offsets[1], object.createTime);
  writer.writeDateTime(offsets[2], object.endTime);
  writer.writeLong(offsets[3], object.localSurgeryId);
  writer.writeBool(offsets[4], object.localVideoMerged);
  writer.writeBool(offsets[5], object.markUploaded);
  writer.writeBool(offsets[6], object.notifyReportCreated);
  writer.writeLong(offsets[7], object.orgId);
  writer.writeLong(offsets[8], object.procedureId);
  writer.writeLong(offsets[9], object.recordStatus);
  writer.writeLong(offsets[10], object.remoteSurgeryId);
  writer.writeString(offsets[11], object.resolution);
  writer.writeLong(offsets[12], object.surgeonId);
  writer.writeBool(offsets[13], object.videoSynced);
}

OfflineSurgeryData _offlineSurgeryDataDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = OfflineSurgeryData();
  object.admissionNumber = reader.readStringOrNull(offsets[0]);
  object.createTime = reader.readDateTime(offsets[1]);
  object.endTime = reader.readDateTimeOrNull(offsets[2]);
  object.id = id;
  object.localSurgeryId = reader.readLong(offsets[3]);
  object.localVideoMerged = reader.readBool(offsets[4]);
  object.markUploaded = reader.readBoolOrNull(offsets[5]);
  object.notifyReportCreated = reader.readBool(offsets[6]);
  object.orgId = reader.readLong(offsets[7]);
  object.procedureId = reader.readLong(offsets[8]);
  object.recordStatus = reader.readLong(offsets[9]);
  object.remoteSurgeryId = reader.readLongOrNull(offsets[10]);
  object.resolution = reader.readString(offsets[11]);
  object.surgeonId = reader.readLong(offsets[12]);
  object.videoSynced = reader.readBool(offsets[13]);
  return object;
}

P _offlineSurgeryDataDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readDateTime(offset)) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readLong(offset)) as P;
    case 4:
      return (reader.readBool(offset)) as P;
    case 5:
      return (reader.readBoolOrNull(offset)) as P;
    case 6:
      return (reader.readBool(offset)) as P;
    case 7:
      return (reader.readLong(offset)) as P;
    case 8:
      return (reader.readLong(offset)) as P;
    case 9:
      return (reader.readLong(offset)) as P;
    case 10:
      return (reader.readLongOrNull(offset)) as P;
    case 11:
      return (reader.readString(offset)) as P;
    case 12:
      return (reader.readLong(offset)) as P;
    case 13:
      return (reader.readBool(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _offlineSurgeryDataGetId(OfflineSurgeryData object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _offlineSurgeryDataGetLinks(
    OfflineSurgeryData object) {
  return [];
}

void _offlineSurgeryDataAttach(
    IsarCollection<dynamic> col, Id id, OfflineSurgeryData object) {
  object.id = id;
}

extension OfflineSurgeryDataQueryWhereSort
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QWhere> {
  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhere>
      anyLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'localSurgeryId'),
      );
    });
  }
}

extension OfflineSurgeryDataQueryWhere
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QWhereClause> {
  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      localSurgeryIdEqualTo(int localSurgeryId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'localSurgeryId',
        value: [localSurgeryId],
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      localSurgeryIdNotEqualTo(int localSurgeryId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [],
              upper: [localSurgeryId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [localSurgeryId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [localSurgeryId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'localSurgeryId',
              lower: [],
              upper: [localSurgeryId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      localSurgeryIdGreaterThan(
    int localSurgeryId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'localSurgeryId',
        lower: [localSurgeryId],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      localSurgeryIdLessThan(
    int localSurgeryId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'localSurgeryId',
        lower: [],
        upper: [localSurgeryId],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterWhereClause>
      localSurgeryIdBetween(
    int lowerLocalSurgeryId,
    int upperLocalSurgeryId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'localSurgeryId',
        lower: [lowerLocalSurgeryId],
        includeLower: includeLower,
        upper: [upperLocalSurgeryId],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension OfflineSurgeryDataQueryFilter
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QFilterCondition> {
  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'admissionNumber',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'admissionNumber',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'admissionNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'admissionNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'admissionNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'admissionNumber',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'admissionNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'admissionNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'admissionNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'admissionNumber',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'admissionNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      admissionNumberIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'admissionNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      createTimeEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createTime',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      createTimeGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createTime',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      createTimeLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createTime',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      createTimeBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      endTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endTime',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      endTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endTime',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      endTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endTime',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      endTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endTime',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      endTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endTime',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      endTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      localSurgeryIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      localSurgeryIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      localSurgeryIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      localSurgeryIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localSurgeryId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      localVideoMergedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localVideoMerged',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      markUploadedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'markUploaded',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      markUploadedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'markUploaded',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      markUploadedEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'markUploaded',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      notifyReportCreatedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notifyReportCreated',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      orgIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'orgId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      orgIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'orgId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      orgIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'orgId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      orgIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'orgId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      procedureIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'procedureId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      procedureIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'procedureId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      procedureIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'procedureId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      procedureIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'procedureId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      recordStatusEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      recordStatusGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recordStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      recordStatusLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recordStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      recordStatusBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recordStatus',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      remoteSurgeryIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'remoteSurgeryId',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      remoteSurgeryIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'remoteSurgeryId',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      remoteSurgeryIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'remoteSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      remoteSurgeryIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'remoteSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      remoteSurgeryIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'remoteSurgeryId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      remoteSurgeryIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'remoteSurgeryId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'resolution',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'resolution',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'resolution',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'resolution',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'resolution',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'resolution',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'resolution',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'resolution',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'resolution',
        value: '',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      resolutionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'resolution',
        value: '',
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      surgeonIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'surgeonId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      surgeonIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'surgeonId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      surgeonIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'surgeonId',
        value: value,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      surgeonIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'surgeonId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterFilterCondition>
      videoSyncedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'videoSynced',
        value: value,
      ));
    });
  }
}

extension OfflineSurgeryDataQueryObject
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QFilterCondition> {}

extension OfflineSurgeryDataQueryLinks
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QFilterCondition> {}

extension OfflineSurgeryDataQuerySortBy
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QSortBy> {
  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByAdmissionNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'admissionNumber', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByAdmissionNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'admissionNumber', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByCreateTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByCreateTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByEndTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByEndTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByLocalSurgeryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByLocalVideoMerged() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localVideoMerged', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByLocalVideoMergedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localVideoMerged', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByMarkUploaded() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'markUploaded', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByMarkUploadedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'markUploaded', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByNotifyReportCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notifyReportCreated', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByNotifyReportCreatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notifyReportCreated', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByOrgId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByOrgIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByProcedureId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'procedureId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByProcedureIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'procedureId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByRecordStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordStatus', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByRecordStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordStatus', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByRemoteSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteSurgeryId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByRemoteSurgeryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteSurgeryId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByResolution() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'resolution', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByResolutionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'resolution', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortBySurgeonId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'surgeonId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortBySurgeonIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'surgeonId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByVideoSynced() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'videoSynced', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      sortByVideoSyncedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'videoSynced', Sort.desc);
    });
  }
}

extension OfflineSurgeryDataQuerySortThenBy
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QSortThenBy> {
  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByAdmissionNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'admissionNumber', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByAdmissionNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'admissionNumber', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByCreateTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByCreateTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByEndTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByEndTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByLocalSurgeryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localSurgeryId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByLocalVideoMerged() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localVideoMerged', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByLocalVideoMergedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localVideoMerged', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByMarkUploaded() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'markUploaded', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByMarkUploadedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'markUploaded', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByNotifyReportCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notifyReportCreated', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByNotifyReportCreatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notifyReportCreated', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByOrgId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByOrgIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByProcedureId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'procedureId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByProcedureIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'procedureId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByRecordStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordStatus', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByRecordStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordStatus', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByRemoteSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteSurgeryId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByRemoteSurgeryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteSurgeryId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByResolution() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'resolution', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByResolutionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'resolution', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenBySurgeonId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'surgeonId', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenBySurgeonIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'surgeonId', Sort.desc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByVideoSynced() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'videoSynced', Sort.asc);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QAfterSortBy>
      thenByVideoSyncedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'videoSynced', Sort.desc);
    });
  }
}

extension OfflineSurgeryDataQueryWhereDistinct
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct> {
  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByAdmissionNumber({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'admissionNumber',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByCreateTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createTime');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByEndTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endTime');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByLocalSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localSurgeryId');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByLocalVideoMerged() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localVideoMerged');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByMarkUploaded() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'markUploaded');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByNotifyReportCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notifyReportCreated');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByOrgId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'orgId');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByProcedureId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'procedureId');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByRecordStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recordStatus');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByRemoteSurgeryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'remoteSurgeryId');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByResolution({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'resolution', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctBySurgeonId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'surgeonId');
    });
  }

  QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QDistinct>
      distinctByVideoSynced() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'videoSynced');
    });
  }
}

extension OfflineSurgeryDataQueryProperty
    on QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QQueryProperty> {
  QueryBuilder<OfflineSurgeryData, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<OfflineSurgeryData, String?, QQueryOperations>
      admissionNumberProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'admissionNumber');
    });
  }

  QueryBuilder<OfflineSurgeryData, DateTime, QQueryOperations>
      createTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createTime');
    });
  }

  QueryBuilder<OfflineSurgeryData, DateTime?, QQueryOperations>
      endTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endTime');
    });
  }

  QueryBuilder<OfflineSurgeryData, int, QQueryOperations>
      localSurgeryIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localSurgeryId');
    });
  }

  QueryBuilder<OfflineSurgeryData, bool, QQueryOperations>
      localVideoMergedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localVideoMerged');
    });
  }

  QueryBuilder<OfflineSurgeryData, bool?, QQueryOperations>
      markUploadedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'markUploaded');
    });
  }

  QueryBuilder<OfflineSurgeryData, bool, QQueryOperations>
      notifyReportCreatedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notifyReportCreated');
    });
  }

  QueryBuilder<OfflineSurgeryData, int, QQueryOperations> orgIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'orgId');
    });
  }

  QueryBuilder<OfflineSurgeryData, int, QQueryOperations>
      procedureIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'procedureId');
    });
  }

  QueryBuilder<OfflineSurgeryData, int, QQueryOperations>
      recordStatusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recordStatus');
    });
  }

  QueryBuilder<OfflineSurgeryData, int?, QQueryOperations>
      remoteSurgeryIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'remoteSurgeryId');
    });
  }

  QueryBuilder<OfflineSurgeryData, String, QQueryOperations>
      resolutionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'resolution');
    });
  }

  QueryBuilder<OfflineSurgeryData, int, QQueryOperations> surgeonIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'surgeonId');
    });
  }

  QueryBuilder<OfflineSurgeryData, bool, QQueryOperations>
      videoSyncedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'videoSynced');
    });
  }
}
