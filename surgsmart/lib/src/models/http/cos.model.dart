import 'package:app_foundation/app_foundation.dart';

///对象存储预签名结果
@ModelDetector()
class CosPreSignUrl extends _CosPreSignUrl {
  late String resourceKey;
  late String presignUrl;
}

///对象存储配置信息
@ModelDetector()
class CosCredential extends _CosCredential {
  late Credential credentials;
  late String region;
  late String bucket;
  late String keyPrefix;
  late String key;
}

@ModelDetector()
class Credential extends _Credential {
  late String accessKeyId;
  late String secretAccessKey;
  late String sessionToken;
  late int expiration;
}

@ModelDetector()
class S3UploadCredential extends _S3UploadCredential {
  late final String key;
  late final String presignType;
  late final int presignExpiresIn;
  late final String presignUrl;
  late final String storageUrl;
}

class _CosPreSignUrl extends AppModel {
  @override
  AppModel extractClone() => CosPreSignUrl();

  @override
  CosPreSignUrl initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as CosPreSignUrl;
    self.resourceKey = map["resource_key"] ?? "";
    self.presignUrl = map["presign_url"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as CosPreSignUrl;
    map["resource_key"] = self.resourceKey;
    map["presign_url"] = self.presignUrl;
    return map;
  }
} // _CosPreSignUrl

class _CosCredential extends AppModel {
  @override
  AppModel extractClone() => CosCredential();

  @override
  CosCredential initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as CosCredential;
    self.credentials = Credential().initWith(map["credentials"] ?? {});
    self.region = map["region"] ?? "";
    self.bucket = map["bucket"] ?? "";
    self.keyPrefix = map["key_prefix"] ?? "";
    self.key = map["key"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as CosCredential;
    map["credentials"] = self.credentials.toMap();
    map["region"] = self.region;
    map["bucket"] = self.bucket;
    map["key_prefix"] = self.keyPrefix;
    map["key"] = self.key;
    return map;
  }
} // _CosCredential

class _Credential extends AppModel {
  @override
  AppModel extractClone() => Credential();

  @override
  Credential initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as Credential;
    self.accessKeyId = map["access_key_id"] ?? "";
    self.secretAccessKey = map["secret_access_key"] ?? "";
    self.sessionToken = map["session_token"] ?? "";
    self.expiration = map["expiration"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as Credential;
    map["access_key_id"] = self.accessKeyId;
    map["secret_access_key"] = self.secretAccessKey;
    map["session_token"] = self.sessionToken;
    map["expiration"] = self.expiration;
    return map;
  }
} // _Credential

class _S3UploadCredential extends AppModel {
  @override
  AppModel extractClone() => S3UploadCredential();

  @override
  S3UploadCredential initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as S3UploadCredential;
    self.key = map["key"] ?? "";
    self.presignType = map["presign_type"] ?? "";
    self.presignExpiresIn = map["presign_expires_in"]?.toInt() ?? 0;
    self.presignUrl = map["presign_url"] ?? "";
    self.storageUrl = map["storage_url"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as S3UploadCredential;
    map["key"] = self.key;
    map["presign_type"] = self.presignType;
    map["presign_expires_in"] = self.presignExpiresIn;
    map["presign_url"] = self.presignUrl;
    map["storage_url"] = self.storageUrl;
    return map;
  }
} // _S3UploadCredential
