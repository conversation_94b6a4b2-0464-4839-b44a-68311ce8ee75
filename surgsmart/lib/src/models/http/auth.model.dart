import 'dart:convert';

import 'package:app_foundation/app_foundation.dart';

/// 授权信息
@ModelDetector()
class AuthorizeInfo extends _AuthorizeInfo {
  ///JWT Access Token
  late final String accessToken;

  ///JWT Refresh Token
  late final String refreshToken;

  ///设备ID
  late final int deviceId;

  ///设备功能支持列表
  late final Features features;

  /// 用户ID
  @FieldIgnore()
  late final int userId;

  @override
  AuthorizeInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map);
    String encodeCode = accessToken.split('.')[1];
    if (encodeCode.isNotEmpty) {
      String decodeCode = utf8.decode(
        base64.decode(base64.normalize(encodeCode)),
      );
      Map<String, dynamic> userInfo = json.decode(decodeCode);
      userId = userInfo["user_id"] ?? 0;
    } else {
      userId = 0;
    }
    return self;
  }
}

///设备功能支持列表
@ModelDetector()
class Features extends _Features {
  ///是否支持调度系统
  late final bool scheduling;
  late final bool networkWired;
  late final bool networkWifi;
  late final bool networkCellular;
}

class _AuthorizeInfo extends AppModel {
  @override
  AppModel extractClone() => AuthorizeInfo();

  @override
  AuthorizeInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as AuthorizeInfo;
    self.accessToken = map["access_token"] ?? "";
    self.refreshToken = map["refresh_token"] ?? "";
    self.deviceId = map["device_id"]?.toInt() ?? 0;
    self.features = Features().initWith(map["features"] ?? {});
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as AuthorizeInfo;
    map["access_token"] = self.accessToken;
    map["refresh_token"] = self.refreshToken;
    map["device_id"] = self.deviceId;
    map["features"] = self.features.toMap();
    return map;
  }
} // _AuthorizeInfo

class _Features extends AppModel {
  @override
  AppModel extractClone() => Features();

  @override
  Features initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as Features;
    self.scheduling = map["scheduling"] ?? false;
    self.networkWired = map["network_wired"] ?? false;
    self.networkWifi = map["network_wifi"] ?? false;
    self.networkCellular = map["network_cellular"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as Features;
    map["scheduling"] = self.scheduling;
    map["network_wired"] = self.networkWired;
    map["network_wifi"] = self.networkWifi;
    map["network_cellular"] = self.networkCellular;
    return map;
  }
} // _Features
