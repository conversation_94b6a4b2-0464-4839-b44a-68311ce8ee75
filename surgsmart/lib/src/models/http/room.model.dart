import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';

/// 会诊授权信息
@ModelDetector()
class RoomAuthorizeInfo extends _RoomAuthorizeInfo {
  ///声网信息
  late final AgoraInfo agora;

  /// WithaiRtc 信息
  late final WithaiRtcInfo withaiRtc;
}

/// WithaiRtc 信息
@ModelDetector()
class WithaiRtcInfo extends _WithaiRtcInfo {
  /// appID
  late final String appId;

  /// token
  late final String token;

  /// 频道名
  late final String channelName;

  /// token 过期时间
  late final int expiresTime;

  /// 声网用户 ID，等价于主刀 user_id
  late final String uid;
}

///声网信息
@ModelDetector()
class AgoraInfo extends _AgoraInfo {
  /// AppID
  late final String appId;

  /// 频道名
  late final String channelName;

  /// 频道 token
  late final String token;

  /// token 过期时间
  late final int expiresTime;

  /// 声网用户 ID，等价于主刀 user_id
  late final int uid;
}

/// 直播分享信息
@ModelDetector()
class RoomShareInfo extends _RoomShareInfo {
  ///分享密钥
  late final String shareKey;
}

enum PlaybackType {
  /// 术中回放
  surgPlay,

  /// 出血回放
  bleedPlay,

  /// 视频播放
  videoPlay,
}

//视频回放信息
class PlaybackInfo {
  final PlaybackType playbackType;
  final List<MergeVideoInfo> videoInfo;
  final BleedingPoint? bleedingPoint;
  final int offsetMs;

  PlaybackInfo({
    required this.playbackType,
    required this.videoInfo,
    this.bleedingPoint,
    this.offsetMs = 0,
  });

  List<String> getVideoPaths() {
    return videoInfo.map((e) => e.path).toList();
  }

  int getTotalDuration() {
    int duration = 0;
    for (var i in videoInfo) {
      duration += i.duration;
    }
    return duration;
  }
}

class MergeVideoInfo {
  String path;
  int duration; //视频真实时长，毫秒

  MergeVideoInfo({required this.path, required this.duration});
}

@ModelDetector()
class InvitationInfo extends _InvitationInfo {
  late final List<InvitationUser> datas;
}

@ModelDetector()
class InvitationUser extends _InvitationUser {
  late final String name;
  late final String avatarUrl;
  late final int id;
  late final String organization;
  late final String department;
  late final bool isMyDepartmentDirector;

  /// 名称拼音
  @FieldIgnore()
  String? namePinyin;
}

class _RoomAuthorizeInfo extends AppModel {
  @override
  AppModel extractClone() => RoomAuthorizeInfo();

  @override
  RoomAuthorizeInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as RoomAuthorizeInfo;
    self.agora = AgoraInfo().initWith(map["agora"] ?? {});
    self.withaiRtc = WithaiRtcInfo().initWith(map["withai_rtc"] ?? {});
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as RoomAuthorizeInfo;
    map["agora"] = self.agora.toMap();
    map["withai_rtc"] = self.withaiRtc.toMap();
    return map;
  }
} // _RoomAuthorizeInfo

class _WithaiRtcInfo extends AppModel {
  @override
  AppModel extractClone() => WithaiRtcInfo();

  @override
  WithaiRtcInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as WithaiRtcInfo;
    self.appId = map["app_id"] ?? "";
    self.token = map["token"] ?? "";
    self.channelName = map["channel_name"] ?? "";
    self.expiresTime = map["expires_time"]?.toInt() ?? 0;
    self.uid = map["uid"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as WithaiRtcInfo;
    map["app_id"] = self.appId;
    map["token"] = self.token;
    map["channel_name"] = self.channelName;
    map["expires_time"] = self.expiresTime;
    map["uid"] = self.uid;
    return map;
  }
} // _WithaiRtcInfo

class _AgoraInfo extends AppModel {
  @override
  AppModel extractClone() => AgoraInfo();

  @override
  AgoraInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as AgoraInfo;
    self.appId = map["app_id"] ?? "";
    self.channelName = map["channel_name"] ?? "";
    self.token = map["token"] ?? "";
    self.expiresTime = map["expires_time"]?.toInt() ?? 0;
    self.uid = map["uid"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as AgoraInfo;
    map["app_id"] = self.appId;
    map["channel_name"] = self.channelName;
    map["token"] = self.token;
    map["expires_time"] = self.expiresTime;
    map["uid"] = self.uid;
    return map;
  }
} // _AgoraInfo

class _RoomShareInfo extends AppModel {
  @override
  AppModel extractClone() => RoomShareInfo();

  @override
  RoomShareInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as RoomShareInfo;
    self.shareKey = map["share_key"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as RoomShareInfo;
    map["share_key"] = self.shareKey;
    return map;
  }
} // _RoomShareInfo

class _InvitationInfo extends AppModel {
  @override
  AppModel extractClone() => InvitationInfo();

  @override
  InvitationInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as InvitationInfo;
    final datas0 = <InvitationUser>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(InvitationUser().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as InvitationInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _InvitationInfo

class _InvitationUser extends AppModel {
  @override
  AppModel extractClone() => InvitationUser();

  @override
  InvitationUser initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as InvitationUser;
    self.name = map["name"] ?? "";
    self.avatarUrl = map["avatar_url"] ?? "";
    self.id = map["id"]?.toInt() ?? 0;
    self.organization = map["organization"] ?? "";
    self.department = map["department"] ?? "";
    self.isMyDepartmentDirector = map["is_my_department_director"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as InvitationUser;
    map["name"] = self.name;
    map["avatar_url"] = self.avatarUrl;
    map["id"] = self.id;
    map["organization"] = self.organization;
    map["department"] = self.department;
    map["is_my_department_director"] = self.isMyDepartmentDirector;
    return map;
  }
} // _InvitationUser
