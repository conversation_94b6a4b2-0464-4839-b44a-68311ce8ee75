import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';

/// 手术信息
@ModelDetector()
class SurgeryInfo extends _SurgeryInfo {
  /// 手术ID
  @FieldAttr(defaultValue: -1)
  late final int id;

  /// 手术时间
  late final int? surgeryTime;

  /// 手术病人住院号
  late final String? admissionNumber;

  /// 手术主刀用户
  late final SurgeryUserInfo? user;

  /// 手术术式
  late final OrgProcedureInfo? procedure;

  late final String? videoKey;

  late final int? liveStatus; //UNKNOWN = 0,ONGOING = 10，STOPPED = 20

  late final int? status; //UNKNOWN = 0,ONGOING = 10，STOPPED = 20

  late final Map? extra;
}

/// 手术用户信息
@ModelDetector()
class SurgeryUserInfo extends _SurgeryUserInfo {
  /// 用户ID
  late final int id;

  /// 用户姓名
  late final String? name;

  /// 用户头像URL
  late final String? avatarUrl;

  /// 用户所属组织
  late final String? organization;

  /// 用户所属科室
  late final String? department;
}

/// 睿标记信息
@ModelDetector()
class SurgeryMarkTimeInfo extends _SurgeryMarkTimeInfo {
  /// 标记ID
  late final int id;

  /// 手术ID
  late final int surgeryId;

  late final String imagePath;
}

/// 手术体腔内外检测状态信息
@ModelDetector()
class SurgeryBodyOutMaskedInfo extends _SurgeryBodyOutMaskedInfo {
  late final bool isBodyoutMasked;
}

class _SurgeryInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryInfo();

  @override
  SurgeryInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryInfo;
    self.id = map["id"]?.toInt() ?? -1;
    self.surgeryTime = map["surgery_time"]?.toInt();
    self.admissionNumber = map["admission_number"];
    if (map["user"] != null) {
      self.user = SurgeryUserInfo().initWith(map["user"]);
    } else {
      self.user = null;
    }
    if (map["procedure"] != null) {
      self.procedure = OrgProcedureInfo().initWith(map["procedure"]);
    } else {
      self.procedure = null;
    }
    self.videoKey = map["video_key"];
    self.liveStatus = map["live_status"]?.toInt();
    self.status = map["status"]?.toInt();
    self.extra = map["extra"];
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryInfo;
    map["id"] = self.id;
    map["surgery_time"] = self.surgeryTime;
    map["admission_number"] = self.admissionNumber;
    map["user"] = self.user?.toMap();
    map["procedure"] = self.procedure?.toMap();
    map["video_key"] = self.videoKey;
    map["live_status"] = self.liveStatus;
    map["status"] = self.status;
    map["extra"] = self.extra;
    return map;
  }
} // _SurgeryInfo

class _SurgeryUserInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryUserInfo();

  @override
  SurgeryUserInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryUserInfo;
    self.id = map["id"]?.toInt() ?? 0;
    self.name = map["name"];
    self.avatarUrl = map["avatar_url"];
    self.organization = map["organization"];
    self.department = map["department"];
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryUserInfo;
    map["id"] = self.id;
    map["name"] = self.name;
    map["avatar_url"] = self.avatarUrl;
    map["organization"] = self.organization;
    map["department"] = self.department;
    return map;
  }
} // _SurgeryUserInfo

class _SurgeryMarkTimeInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryMarkTimeInfo();

  @override
  SurgeryMarkTimeInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryMarkTimeInfo;
    self.id = map["id"]?.toInt() ?? 0;
    self.surgeryId = map["surgery_id"]?.toInt() ?? 0;
    self.imagePath = map["image_path"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryMarkTimeInfo;
    map["id"] = self.id;
    map["surgery_id"] = self.surgeryId;
    map["image_path"] = self.imagePath;
    return map;
  }
} // _SurgeryMarkTimeInfo

class _SurgeryBodyOutMaskedInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryBodyOutMaskedInfo();

  @override
  SurgeryBodyOutMaskedInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryBodyOutMaskedInfo;
    self.isBodyoutMasked = map["is_bodyout_masked"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryBodyOutMaskedInfo;
    map["is_bodyout_masked"] = self.isBodyoutMasked;
    return map;
  }
} // _SurgeryBodyOutMaskedInfo
