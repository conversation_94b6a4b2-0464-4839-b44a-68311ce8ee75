import 'package:app_foundation/app_foundation.dart';

/// 术式扩展标识
@ModelDetector()
class ProcedureFeatureFlags extends _ProcedureFeatureFlags {
  late final bool? isRobotProcedure;
  late final bool? isStaplerAmputationDisabled;
}

/// 组织术式列表
@ModelDetector()
class OrgProcedureListInfo extends _OrgProcedureListInfo {
  late final List<OrgProcedureInfo> datas;
}

/// 组织术式信息
@ModelDetector()
class OrgProcedureInfo extends _OrgProcedureInfo {
  /// 术式ID
  late final int id;

  /// 术式名字
  late final String name;

  late final String code;

  //算法代号
  late final String algorithmCode;

  //组织id
  late final int medicalOrganizationId;

  //术式标识
  late final ProcedureFeatureFlags? featureFlags;
}

class _ProcedureFeatureFlags extends AppModel {
  @override
  AppModel extractClone() => ProcedureFeatureFlags();

  @override
  ProcedureFeatureFlags initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as ProcedureFeatureFlags;
    self.isRobotProcedure = map["is_robot_procedure"];
    self.isStaplerAmputationDisabled = map["is_stapler_amputation_disabled"];
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as ProcedureFeatureFlags;
    map["is_robot_procedure"] = self.isRobotProcedure;
    map["is_stapler_amputation_disabled"] = self.isStaplerAmputationDisabled;
    return map;
  }
} // _ProcedureFeatureFlags

class _OrgProcedureListInfo extends AppModel {
  @override
  AppModel extractClone() => OrgProcedureListInfo();

  @override
  OrgProcedureListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as OrgProcedureListInfo;
    final datas0 = <OrgProcedureInfo>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(OrgProcedureInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as OrgProcedureListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _OrgProcedureListInfo

class _OrgProcedureInfo extends AppModel {
  @override
  AppModel extractClone() => OrgProcedureInfo();

  @override
  OrgProcedureInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as OrgProcedureInfo;
    self.id = map["id"]?.toInt() ?? 0;
    self.name = map["name"] ?? "";
    self.code = map["code"] ?? "";
    self.algorithmCode = map["algorithm_code"] ?? "";
    self.medicalOrganizationId = map["medical_organization_id"]?.toInt() ?? 0;
    if (map["feature_flags"] != null) {
      self.featureFlags = ProcedureFeatureFlags().initWith(map["feature_flags"]);
    } else {
      self.featureFlags = null;
    }
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as OrgProcedureInfo;
    map["id"] = self.id;
    map["name"] = self.name;
    map["code"] = self.code;
    map["algorithm_code"] = self.algorithmCode;
    map["medical_organization_id"] = self.medicalOrganizationId;
    map["feature_flags"] = self.featureFlags?.toMap();
    return map;
  }
} // _OrgProcedureInfo
