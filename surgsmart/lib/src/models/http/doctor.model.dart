import 'package:app_foundation/app_foundation.dart';

@ModelDetector()
class DoctorListInfo extends _DoctorListInfo {
  late final List<DoctorInfo> datas;
}

/// 医生信息
@ModelDetector()
class DoctorInfo extends _DoctorInfo {
  ///用户ID
  @FieldAttr(defaultValue: -1)
  late final int id;

  ///姓名
  @FieldAttr(defaultValue: "-")
  late final String name;

  ///头像地址
  late final String avatarUrl;

  ///单位
  late final String department;

  ///组织
  @FieldAttr(defaultValue: "-")
  late final String organization;

  /// 用于标记协同用户麦克风是否开启
  bool isMicrophoneActive = false;

  /// 用于标记协同用户是否正在说话
  @FieldIgnore()
  bool speaking = false;

  /// 上次说话时间
  @FieldIgnore()
  int lastSpeakingTime = 0;

  /// 是否是新加入
  @FieldIgnore()
  bool isNewJoin = false;
}

/// 组织主刀列表
@ModelDetector()
class OrgSurgeonListInfo extends _OrgSurgeonListInfo {
  late final List<OrgSurgeonInfo> datas;
}

/// 组织主刀信息
@ModelDetector()
class OrgSurgeonInfo extends _OrgSurgeonInfo {
  /// 主刀ID
  late final int id;

  /// 主刀名字
  late final String name;

  //加入的组织
  late final List<int> medicalOrganizationIds;
}

class _DoctorListInfo extends AppModel {
  @override
  AppModel extractClone() => DoctorListInfo();

  @override
  DoctorListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DoctorListInfo;
    final datas0 = <DoctorInfo>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(DoctorInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DoctorListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _DoctorListInfo

class _DoctorInfo extends AppModel {
  @override
  AppModel extractClone() => DoctorInfo();

  @override
  DoctorInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DoctorInfo;
    self.id = map["id"]?.toInt() ?? -1;
    self.name = map["name"] ?? "-";
    self.avatarUrl = map["avatar_url"] ?? "";
    self.department = map["department"] ?? "";
    self.organization = map["organization"] ?? "-";
    self.isMicrophoneActive = map["is_microphone_active"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DoctorInfo;
    map["id"] = self.id;
    map["name"] = self.name;
    map["avatar_url"] = self.avatarUrl;
    map["department"] = self.department;
    map["organization"] = self.organization;
    map["is_microphone_active"] = self.isMicrophoneActive;
    return map;
  }
} // _DoctorInfo

class _OrgSurgeonListInfo extends AppModel {
  @override
  AppModel extractClone() => OrgSurgeonListInfo();

  @override
  OrgSurgeonListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as OrgSurgeonListInfo;
    final datas0 = <OrgSurgeonInfo>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(OrgSurgeonInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as OrgSurgeonListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _OrgSurgeonListInfo

class _OrgSurgeonInfo extends AppModel {
  @override
  AppModel extractClone() => OrgSurgeonInfo();

  @override
  OrgSurgeonInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as OrgSurgeonInfo;
    self.id = map["id"]?.toInt() ?? 0;
    self.name = map["name"] ?? "";
    final medicalOrganizationIds0 = <int>[];
    self.medicalOrganizationIds = medicalOrganizationIds0;
    map["medical_organization_ids"]?.forEach((element) {
      medicalOrganizationIds0.add(element?.toInt() ?? 0);
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as OrgSurgeonInfo;
    map["id"] = self.id;
    map["name"] = self.name;
    map["medical_organization_ids"] = self.medicalOrganizationIds.map((element) {
      return element;
    }).toList();
    return map;
  }
} // _OrgSurgeonInfo
