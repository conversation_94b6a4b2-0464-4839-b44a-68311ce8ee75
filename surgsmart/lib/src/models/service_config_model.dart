import 'package:app_foundation/app_foundation.dart';

@ModelDetector()
class ServiceConfig extends _ServiceConfig {
  late final ServiceUrls surgsmartUrls;
  late final FeatureFlags featureFlags;
}

@ModelDetector()
class ServiceUrls extends _ServiceUrls {
  late final String surgsmartWebUrl;
  late final String surgsmartVideoUrl;
  late final String surgsmartMqttHookUrl;
  late final String surgsmartMqttUrlForWeb;
  late final String surgsmartMqttUrlForApp;
  late final String surgsmartSamUrl;
  late final String surgsmartWithaiRtcUrl;
}

@ModelDetector()
class FeatureFlags extends _FeatureFlags {
  late final bool authUsernamePassword;
  late final bool authPhonePassword;
  late final bool authEmailPassword;
  late final bool authPhoneVerificationCode;
  late final bool authEmailVerificationCode;
  late final bool authWechat;
  late final bool surgeryUserUpload;
  late final bool collaborationUseWithaiRtc;
  late final bool surgeryLive;
}

class _ServiceConfig extends AppModel {
  @override
  AppModel extractClone() => ServiceConfig();

  @override
  ServiceConfig initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as ServiceConfig;
    self.surgsmartUrls = ServiceUrls().initWith(map["surgsmart_urls"] ?? {});
    self.featureFlags = FeatureFlags().initWith(map["feature_flags"] ?? {});
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as ServiceConfig;
    map["surgsmart_urls"] = self.surgsmartUrls.toMap();
    map["feature_flags"] = self.featureFlags.toMap();
    return map;
  }
} // _ServiceConfig

class _ServiceUrls extends AppModel {
  @override
  AppModel extractClone() => ServiceUrls();

  @override
  ServiceUrls initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as ServiceUrls;
    self.surgsmartWebUrl = map["surgsmart_web_url"] ?? "";
    self.surgsmartVideoUrl = map["surgsmart_video_url"] ?? "";
    self.surgsmartMqttHookUrl = map["surgsmart_mqtt_hook_url"] ?? "";
    self.surgsmartMqttUrlForWeb = map["surgsmart_mqtt_url_for_web"] ?? "";
    self.surgsmartMqttUrlForApp = map["surgsmart_mqtt_url_for_app"] ?? "";
    self.surgsmartSamUrl = map["surgsmart_sam_url"] ?? "";
    self.surgsmartWithaiRtcUrl = map["surgsmart_withai_rtc_url"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as ServiceUrls;
    map["surgsmart_web_url"] = self.surgsmartWebUrl;
    map["surgsmart_video_url"] = self.surgsmartVideoUrl;
    map["surgsmart_mqtt_hook_url"] = self.surgsmartMqttHookUrl;
    map["surgsmart_mqtt_url_for_web"] = self.surgsmartMqttUrlForWeb;
    map["surgsmart_mqtt_url_for_app"] = self.surgsmartMqttUrlForApp;
    map["surgsmart_sam_url"] = self.surgsmartSamUrl;
    map["surgsmart_withai_rtc_url"] = self.surgsmartWithaiRtcUrl;
    return map;
  }
} // _ServiceUrls

class _FeatureFlags extends AppModel {
  @override
  AppModel extractClone() => FeatureFlags();

  @override
  FeatureFlags initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as FeatureFlags;
    self.authUsernamePassword = map["auth_username_password"] ?? false;
    self.authPhonePassword = map["auth_phone_password"] ?? false;
    self.authEmailPassword = map["auth_email_password"] ?? false;
    self.authPhoneVerificationCode = map["auth_phone_verification_code"] ?? false;
    self.authEmailVerificationCode = map["auth_email_verification_code"] ?? false;
    self.authWechat = map["auth_wechat"] ?? false;
    self.surgeryUserUpload = map["surgery_user_upload"] ?? false;
    self.collaborationUseWithaiRtc = map["collaboration_use_withai_rtc"] ?? false;
    self.surgeryLive = map["surgery_live"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as FeatureFlags;
    map["auth_username_password"] = self.authUsernamePassword;
    map["auth_phone_password"] = self.authPhonePassword;
    map["auth_email_password"] = self.authEmailPassword;
    map["auth_phone_verification_code"] = self.authPhoneVerificationCode;
    map["auth_email_verification_code"] = self.authEmailVerificationCode;
    map["auth_wechat"] = self.authWechat;
    map["surgery_user_upload"] = self.surgeryUserUpload;
    map["collaboration_use_withai_rtc"] = self.collaborationUseWithaiRtc;
    map["surgery_live"] = self.surgeryLive;
    return map;
  }
} // _FeatureFlags
