import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';

@ModelDetector()
class ExpandScreenStatusInfo extends _ExpandScreenStatusInfo {
  late final bool surgeryIsStart; //手术是否开始
  late final bool videoIsFuzzy; //视频是否模糊
  late final int videoBitrate; //码率
  late final List<DoctorInfo> doctors; //协同人员列表
  late final int? liveUserCount; // 观看直播用户数量
  late final bool hasSignal; //是否有采集信号
  late final bool isPlayback; //是否是回放
  late final String? externalVideoDevice; //扩展视频设备Path
  late final String? procedureName; //术式名称
  late final String? networkType; //网络类型
}

class _ExpandScreenStatusInfo extends AppModel {
  @override
  AppModel extractClone() => ExpandScreenStatusInfo();

  @override
  ExpandScreenStatusInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as ExpandScreenStatusInfo;
    self.surgeryIsStart = map["surgery_is_start"] ?? false;
    self.videoIsFuzzy = map["video_is_fuzzy"] ?? false;
    self.videoBitrate = map["video_bitrate"]?.toInt() ?? 0;
    final doctors0 = <DoctorInfo>[];
    self.doctors = doctors0;
    map["doctors"]?.forEach((element) {
      doctors0.add(DoctorInfo().initWith(element.cast<String, dynamic>()));
    });
    self.liveUserCount = map["live_user_count"]?.toInt();
    self.hasSignal = map["has_signal"] ?? false;
    self.isPlayback = map["is_playback"] ?? false;
    self.externalVideoDevice = map["external_video_device"];
    self.procedureName = map["procedure_name"];
    self.networkType = map["network_type"];

    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as ExpandScreenStatusInfo;
    map["surgery_is_start"] = self.surgeryIsStart;
    map["video_is_fuzzy"] = self.videoIsFuzzy;
    map["video_bitrate"] = self.videoBitrate;
    map["doctors"] =
        self.doctors.map((element) {
          return element.toMap();
        }).toList();
    map["live_user_count"] = self.liveUserCount;
    map["has_signal"] = self.hasSignal;
    map["is_playback"] = self.isPlayback;
    map["external_video_device"] = self.externalVideoDevice;
    map["procedure_name"] = self.procedureName;
    map["network_type"] = self.networkType;

    return map;
  }
} // _ExpandScreenStatusInfo
