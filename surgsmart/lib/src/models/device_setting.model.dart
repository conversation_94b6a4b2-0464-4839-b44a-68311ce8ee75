import 'package:app_foundation/app_foundation.dart';

@ModelDetector()
class DeviceSettingInfo extends _DeviceSettingInfo {
  late String device;
  late dynamic value;
  late dynamic info;
  late bool selected = false;
}

class _DeviceSettingInfo extends AppModel {
  @override
  AppModel extractClone() => DeviceSettingInfo();

  @override
  DeviceSettingInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DeviceSettingInfo;
    self.device = map["device"] ?? "";
    self.value = map["value"];
    self.info = map["info"] ?? "";
    self.selected = map["selected"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DeviceSettingInfo;
    map["device"] = self.device;
    map["value"] = self.value;
    map["info"] = self.info;
    map["selected"] = self.selected;
    return map;
  }
} // _DeviceSettingInfo
