/// 此处枚举顺序不能改变，否则会与协议中的值对应不上
enum ClientMsgType {
  //mtu大小通知
  mtuNotify,
  //设备授权信息请求
  authLoginRequest,
  //重启设备
  restartDevice,
  //关闭设备
  shutdownDevice,
  //重启应用
  restartApp,
  //开启手术
  surgeryStart,
//停止手术
  surgeryStop,
//开启直播
  liveStart,
//停止直播
  liveStop,
//睿标记
  markTime,
//设置麦克风
  microphoneState,
//体腔外模糊
  bodyoutMask,
//设备设置获取
  settingRead,
  //手术信息读取
  surgeryRead,
//下发类型
//App状态
  appStatus,
  //手术状态
  surgeryStatus,
  //设备控制状态
  controlStatus,
  /* ble新增状态 */
  //主机断开
  m3Disconnect,
  //存储空间已满
  diskFull,
  //网络连接状态
  networkState,
  //手术开始时间
  surgeryTime,
  //协同人员麦克风
  synergyMicrophoneState,
  //请求m3断开：22
  requestM3Disconnect,
}
