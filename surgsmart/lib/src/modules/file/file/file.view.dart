import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:surgsmart/src/modules/file/file/widgets/file_list.widget.dart';
import 'package:surgsmart/src/modules/file/file/widgets/file_list_footer.widget.dart';
import 'package:surgsmart/src/modules/file/file/widgets/file_list_header.widget.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';
import 'file.controller.dart';

/// 所属模块: file
///
/// 文件
class FileView extends AppView<FileController> {
  const FileView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      body: AppBackground(
        child: controller.build(
          onSuccess: onSuccess,
          onFailure: onFailure,
          onLoading: onLoading,
          onEmpty: onEmpty,
        ),
      ),
    );
  }

  Widget onSuccess(BuildContext context, List<OfflineSurgeryData> fileList) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 180.w, vertical: 60.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ValueListenableBuilder(
              valueListenable: controller.checkEmpty,
              builder: (context, value, child) {
                return FileListHeader(
                  controller: controller,
                  doctors: controller.doctors,
                  procedures: controller.procedures,
                  onUpdateAdmissionNumber: (admissionNumber) {
                    controller.admissionNumber = admissionNumber;
                    controller.page = 1;
                    controller.loadLocalFile();
                  },
                  onUpdateProcedure: (procedureInfo) {
                    controller.procedure = procedureInfo;
                    controller.page = 1;
                    controller.loadLocalFile();
                  },
                  onUpdateUserId: (id) {
                    controller.userId = id;
                    controller.page = 1;
                    controller.loadLocalFile();
                  },
                  onUpdateDateTime: ({endTime, startTime}) {
                    controller.endTime = endTime;
                    controller.startTime = startTime;
                    controller.page = 1;
                    controller.loadLocalFile();
                  },
                  onExport: () async {
                    await controller.showChooseDiskModal(
                        controller.selectedFiles.values.toList());
                  },
                  ondDeleteAll: () async {
                    controller.showDeleteModal(
                        controller.selectedFiles.values.toList());
                  },
                );
              }),
          ValueListenableBuilder(
              valueListenable: controller.surgeryFileListInfo,
              builder: (context, value, child) {
                return FileList(
                  controller: controller,
                  list: value,
                  onDelete: (file) async {
                    controller.showDeleteModal([file]);
                  },
                  onExport: (file) async {
                    await controller.showChooseDiskModal([file]);
                  },
                );
              }),
          ValueListenableBuilder(
            valueListenable: controller.spaceInfo,
            builder: (context, value, child) {
              return FileListFooter(
                page: controller.page,
                pageSize: 4,
                total: controller.total,
                chooseTotal: controller.selectedFiles.length,
                spaceInfo: value,
                onUpdatePage: (int page) {
                  controller.page = page;
                  controller.loadLocalFile();
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return Scaffold(
      appBar: AppBar(
          toolbarHeight: 180.h,
          backgroundColor: Colors.transparent,
          centerTitle: true,
          leadingWidth: 480.w,
          leading: Padding(
            padding: EdgeInsets.only(left: 100.w),
            child: Button(
              betweenSpace: 24.w,
              icon: Icon(
                Icons.arrow_back_ios_new_rounded,
                size: 56.r,
                color: Colors.white,
              ),
              label: Text(
                S.current.f_7VB8izF1,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 56.sp,
                  height: 78 / 56,
                ),
              ),
              onPressed: context.pop,
            ),
          ),
          title: Text(
            S.current.f_43uAm9gr,
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 80.sp,
              height: 112 / 80,
            ),
          )),
      body: Center(child: Text(error)),
    );
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
