import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:isar/isar.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/file.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:surgsmart/src/modules/file/file/data_sync_manager.dart';
import 'package:surgsmart/src/modules/file/file/widgets/export_progress.widget.dart';
import 'package:surgsmart/src/modules/file/file/widgets/extra_disk_list.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/db_utils.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';

/// 所属模块: file
///
/// 文件
class FileController extends AppController
    with StateMixin<List<OfflineSurgeryData>> {
  FileController(super.key, super.routerState);

  int page = 1;

  int total = 0;

  int pageSize = 4;

  String? admissionNumber;

  int? userId;

  OrgProcedureInfo? procedure;

  int? startTime;

  int? endTime;

  /// 术式列表
  List<OrgProcedureInfo> procedures = [];

  /// 主刀医生列表
  List<OrgSurgeonInfo> doctors = [];

  /// 选择的文件的路径列表
  List<SurgeryFileAttribute> fileAttributes = [];

  /// 选择的磁盘路径
  final selectedUsbPath = "".nullableNotifier;

  // 磁盘信息
  final spaceInfo = HostDevice.share.getSpaceInfo().notifier;

  final checkAll = false.notifier;

  final checkEmpty = true.notifier;

  var exportedProgress = 0.0.notifier;

  int exported = 0;

  static int allExported = 0;

  final surgeryFileListInfo = <OfflineSurgeryData>[].notifier;

  /// 选中的文件列表
  Map<int, OfflineSurgeryData> selectedFiles = {};

  Timer? timer;

  bool isShowEndExportDialog = false;
  static String? destinationFilePath;

  @override
  void onInit() {}

  @override
  void onReady() async {
    try {
      //上下游关系，先加载术式和医生数据，再加载文件数据
      readPreferences();
      await queryCount();
      update(LoadState.success(await loadLocalFile()));
    } catch (e) {
      update(LoadState.failure(S.current.f_N8DiLkCJ));
    }

    // 更新文件列表
    timer = Timer.periodic(
      const Duration(seconds: 5),
      (Timer timer) async => await loadLocalFile(),
    );
  }

  @override
  void onClose() {
    timer?.cancel();
    spaceInfo.dispose();
    checkAll.dispose();
    checkEmpty.dispose();
    selectedUsbPath.dispose();
    exportedProgress.dispose();
  }

  static cancelFileExport() {
    Helper.share.cancelTask(destinationFilePath ?? '');
  }

  /// 获取本地文件列表
  Future<List<OfflineSurgeryData>> loadLocalFile() async {
    QueryBuilder<OfflineSurgeryData, OfflineSurgeryData, QFilterCondition>
    queryBuilder = DbUtils().isar.offlineSurgeryDatas.filter();

    if (userId != null) {
      queryBuilder = queryBuilder.surgeonIdEqualTo(userId!);
    }
    if (procedure?.id != null) {
      queryBuilder = queryBuilder.procedureIdEqualTo(procedure!.id);
    }
    if (admissionNumber != null) {
      queryBuilder = queryBuilder.admissionNumberEqualTo(admissionNumber);
    }
    if (startTime != null && endTime != null) {
      queryBuilder = queryBuilder.createTimeBetween(
        DateTime.fromMillisecondsSinceEpoch(startTime!),
        DateTime.fromMillisecondsSinceEpoch(endTime!),
      );
    }
    List<OfflineSurgeryData> fileList = [];
    if (queryBuilder
        is QueryBuilder<
          OfflineSurgeryData,
          OfflineSurgeryData,
          QAfterFilterCondition
        >) {
      fileList =
          await queryBuilder
              .recordStatusEqualTo(0)
              .sortByCreateTimeDesc()
              .offset((page - 1) * pageSize)
              .limit(pageSize)
              .findAll();
    } else {
      fileList =
          await DbUtils().isar.offlineSurgeryDatas
              .filter()
              .recordStatusEqualTo(0)
              .sortByCreateTimeDesc()
              .offset((page - 1) * pageSize)
              .limit(pageSize)
              .findAll();
    }

    spaceInfo.value = HostDevice.share.getSpaceInfo();
    //查询总数,过期数据删除可能导致总数的变化
    queryCount();

    //没有变化则不刷新
    checkRefresh(fileList);
    return fileList;
  }

  /// 删除文件
  Future<void> deleteFile(List<OfflineSurgeryData> datas) async {
    await DbUtils().softDeleteSurgery(datas);
    DataSyncManager().removeSyncTask(datas);
    if (((page - 1) * pageSize) + 1 > total - datas.length) {
      page = 1;
    }
    loadLocalFile();
    //删除合并的视频文件
    deleteMergedVideo(datas);
    // 删除手术文件夹
    deleteSurgeryFolder(datas);
  }

  void readPreferences() {
    doctors =
        OrgSurgeonListInfo()
            .initWith(
              jsonDecode(AppPreferences.surgeonInfo.stringValue ?? '{}'),
            )
            .datas;
    procedures =
        OrgProcedureListInfo()
            .initWith(
              jsonDecode(AppPreferences.procedureInfo.stringValue ?? '{}'),
            )
            .datas;
  }

  // 添加选择的文件
  void addChecked(List<OfflineSurgeryData> list) async {
    selectedFiles.addEntries(list.map(((e) => MapEntry(e.id, e))));
    checkEmpty.value = selectedFiles.isEmpty;
    checkAll.value = selectedFiles.length == surgeryFileListInfo.value.length;
    await loadLocalFile();
  }

  void removeChecked(List<OfflineSurgeryData> list) async {
    if (list.isEmpty) return;
    selectedFiles.removeWhere(
      (key, value) => list.any((element) => element.id == key),
    );
    checkEmpty.value = selectedFiles.isEmpty;
    checkAll.value = selectedFiles.length == surgeryFileListInfo.value.length;
    await loadLocalFile();
  }

  void clearChecked() async {
    selectedFiles = {};
    checkEmpty.value = true;
    checkAll.value = false;
    await loadLocalFile();
  }

  /// 删除提示弹窗
  Future<void> showDeleteModal(List<OfflineSurgeryData> list) async {
    final unsynchronizedDatas =
        list.where((e) => e.videoSynced == false).toList();
    String message = '';
    if (list.length > 1) {
      message = StringUtil.replaceInterpolation(S.current.f1_N4rZ3qCB, [
        list.length,
      ]);
    } else {
      message = S.current.f_9KZDin17;
    }

    if (unsynchronizedDatas.isNotEmpty) {
      if (unsynchronizedDatas.length > 1) {
        message += StringUtil.replaceInterpolation(S.current.f1_N4IyLhdc, [
          unsynchronizedDatas.length,
        ]);
      } else {
        message += S.current.f_N4IyxRoP;
      }
    }
    Modal(
      title: S.current.f_9KZDxs6w,
      cancelText: S.current.f_9KZD45AL,
      confirmText: S.current.f_9KZDYDv7,
      kind: ModalKind.dialog,
      type: ModalType.error,
      message: message,
      onConfirm: () async {
        try {
          await deleteFile(list);
          app.logW("删除手术文件数量：${list.length}");
          removeChecked(list);
          ToastUtils.showToast(
            message: S.current.f_9aUbGUbm,
            type: ToastType.success,
          );
          context?.pop();
        } catch (e) {
          app.logW("删除手术文件出错-ids：${list.toString()}");
          ToastUtils.showToast(message: '删除出错', type: ToastType.error);
        }
      },
    ).show(context!, barrierDismissible: false);
  }

  // 导出选择弹窗
  Future<void> showChooseDiskModal(List<OfflineSurgeryData> list) async {
    isShowEndExportDialog = false;
    await getFileAtteributes(list);
    if (list.length == 1 && fileAttributes.isEmpty) {
      ToastUtils.showToast(message: '未找到视频文件', type: ToastType.error);
      return;
    }

    // 校验外接 U 盘大小和所选文件的所有大小
    int totalFileSize = 0;
    for (var item in fileAttributes) {
      totalFileSize += item.fileSize;
    }

    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xff5CF5EB).withValues(alpha: 0.7),
        const Color(0xff56B3E3).withValues(alpha: 0.7),
      ],
    );
    Modal(
      title: S.current.f_IyINoY1L,
      kind: ModalKind.modal,
      type: ModalType.info,
      onClose: () {
        selectedUsbPath.value = null;
        context?.pop();
      },
      footer: ValueListenableBuilder(
        valueListenable: selectedUsbPath,
        builder: (context, value, child) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 64.w, vertical: 64.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    selectedUsbPath.value = null;
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 180.h,
                    padding: EdgeInsets.symmetric(horizontal: 160.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white, width: 4.w),
                      borderRadius: BorderRadius.circular(90.r),
                    ),
                    child: Text(
                      S.current.f_9KZD45AL,
                      style: TextStyle(fontSize: 72.sp, color: Colors.white),
                    ),
                  ),
                ),
                SizedBox(width: 64.w),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (selectedUsbPath.value == null) {
                      return;
                    } else {
                      context.pop();
                      startExportModal();
                    }
                  },
                  child: Opacity(
                    opacity: selectedUsbPath.value == null ? 0.4 : 1,
                    child: Container(
                      alignment: Alignment.center,
                      height: 180.h,
                      padding: EdgeInsets.symmetric(horizontal: 160.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(90.r),
                        gradient: gradient,
                      ),
                      child: Text(
                        S.current.f_9KZD5TVS,
                        style: TextStyle(fontSize: 72.sp, color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      child: ExtraDiskList(
        selectedUsbPath: selectedUsbPath.value,
        totalFileSize: totalFileSize,
        onExport: (path, {size = 0}) {
          selectedUsbPath.value = path;
        },
        onPullout: () {
          if (selectedUsbPath.value != null) {
            selectedUsbPath.value = null;
            context?.pop();
            showChooseDiskModal(list);
          }
        },
      ),
    ).show(context!, barrierDismissible: false);
  }

  // 开启导出操作
  void startExportModal() {
    // 执行导出操作
    allExported = fileAttributes.length;
    startExport();
    Modal(
      title: S.current.f_N6JlDFfG,
      kind: ModalKind.result,
      type: ModalType.error,
      closable: false,
      confirmText: S.current.f_9KZDW7PY,
      child: ValueListenableBuilder(
        valueListenable: exportedProgress,
        builder: (context, value, child) {
          return ExportProgress(
            all: allExported,
            exported: exported,
            exportedProgress: exportedProgress.value,
          );
        },
      ),
      onConfirm: () async {
        showStopExportDialog(app.context);
      },
    ).show(app.context, barrierDismissible: false);
  }

  /// 停止导出弹窗
  void showStopExportDialog(BuildContext context) {
    isShowEndExportDialog = true;
    Modal(
      title: S.current.f_9KZDW7PY,
      kind: ModalKind.dialog,
      type: ModalType.error,
      confirmText: S.current.f_9KZDW7PY,
      cancelText: S.current.f_9KZD45AL,
      message: S.current.f_N71KjkOq,
      onConfirm: () async {
        /// 停止导出
        cancelFileExport();
        context.pop();
        isShowEndExportDialog = false;
      },
      onCancel: () {
        isShowEndExportDialog = false;
        context.pop();
      },
      onClose: () {
        isShowEndExportDialog = false;
        context.pop();
      },
    ).show(context, barrierDismissible: false);
  }

  /// 导出完成弹窗
  void showExportFinishDialog(BuildContext context) {
    Modal(
      title: S.current.f_zjLzaAir,
      kind: ModalKind.result,
      type: ModalType.info,
      message: S.current.f_9KZDvxul,
      confirmText: S.current.f_9KZD5TVS,
      onConfirm: () async {
        /// 结束
        context.pop();
        if (isShowEndExportDialog) {
          context.pop();
        }
        selectedUsbPath.value = null;
      },
      onClose: () {
        context.pop();
        selectedUsbPath.value = null;
      },
    ).show(context, barrierDismissible: false);
  }

  /// 导出出错弹窗
  void showExportErrorDialog(BuildContext context) {
    Modal(
      title: S.current.f_zBM3LFAZ,
      kind: ModalKind.result,
      type: ModalType.error,
      closable: false,
      message: S.current.f_N71Kd8uQ,
      confirmText: S.current.f_9KZD5TVS,
      onConfirm: () async {
        context.pop();
        if (isShowEndExportDialog) {
          context.pop();
        }
        selectedUsbPath.value = null;
      },
      onClose: () {
        context.pop();
        if (isShowEndExportDialog) {
          context.pop();
        }
        selectedUsbPath.value = null;
      },
    ).show(context, barrierDismissible: false);
  }

  /// 执行导出操作
  Future<void> startExport() async {
    // 递归实现
    updateExportProgress(
      fileAttributes,
      usbPath: selectedUsbPath.value.toString(),
    );
  }

  /// 导出进度
  void updateExportProgress(
    List<SurgeryFileAttribute> fileList, {
    required String usbPath,
  }) {
    var currentFile = fileList[0];
    String sourcePath = currentFile.filePath;
    String destinationDir = "$usbPath/SurgSmart";
    Directory directory = Directory(destinationDir);
    if (!directory.existsSync()) {
      directory.createSync(recursive: true);
    }
    destinationFilePath = '$destinationDir/${currentFile.fileExportName}';

    Helper.share.copyFile(
      src: sourcePath,
      dst: destinationFilePath!,
      callback: (ffi.CopyFileInfo info) {
        if (info.state == CopyFileState.success) {
          exported += 1;
          exportedProgress.value = 1;
          if (fileList.length > 1) {
            updateExportProgress(
              fileList.sublist(1, fileList.length),
              usbPath: usbPath,
            );
          } else {
            if (context == null || selectedUsbPath.value == null) return;
            // 导出完成
            context?.pop();
            showExportFinishDialog(context!);
            exportedProgress.value = 0;
            exported = 0;
            allExported = 0;
          }
        } else if (info.state == CopyFileState.failed) {
          allExported = 0;
          if (context == null || selectedUsbPath.value == null) return;
          context?.pop();
          showExportErrorDialog(context!);
        } else if (info.state == CopyFileState.canceled) {
          context?.pop();
          selectedUsbPath.value = null;
          allExported = 0;
        } else {
          exportedProgress.value = info.progress;
        }
      },
    );
  }

  Future<void> getFileAtteributes(List<OfflineSurgeryData> list) async {
    fileAttributes = [];
    for (var data in list) {
      final videoPath =
          '${AppContext.share.documentsPath}/v202310/merged/${data.localSurgeryId}.mp4';
      File file = File(videoPath);
      if (!file.existsSync()) {
        continue;
      }
      SurgeryFileAttribute attribute = SurgeryFileAttribute();
      FileStat fileStat = await file.stat();
      attribute.fileSize = fileStat.size;
      attribute.filePath = videoPath;
      attribute.id = data.localSurgeryId;
      attribute.fileExportName = "${data.localSurgeryId}.mp4";
      fileAttributes.add(attribute);
    }
  }

  void checkRefresh(List<OfflineSurgeryData> fileList) {
    if (fileList.length == surgeryFileListInfo.value.length) {
      bool isSame = true;
      for (int i = 0; i < fileList.length; i++) {
        var oldFile = surgeryFileListInfo.value[i];
        var newFile = fileList[i];
        if (newFile.localVideoMerged != oldFile.localVideoMerged ||
            newFile.videoSynced != oldFile.videoSynced ||
            newFile.localSurgeryId != oldFile.localSurgeryId) {
          isSame = false;
        }
      }
      if (isSame) return;
    }
    surgeryFileListInfo.value = fileList;
  }

  static Future<void> clearPlayback(int localSurgeryId) async {
    final path =
        '${AppContext.share.documentsPath}/v202310/$localSurgeryId/playback/';
    final playbackDir = Directory(path);
    if (playbackDir.existsSync()) {
      try {
        playbackDir.delete(recursive: true);
      } catch (e) {
        app.logE("删除播放文件夹失败：$e");
      }
    }
  }

  Future<int> queryCount() async {
    total =
        await DbUtils().isar.offlineSurgeryDatas
            .filter()
            .recordStatusEqualTo(0)
            .count();
    return total;
  }

  static Future<void> deleteMergedVideo(List<OfflineSurgeryData> datas) async {
    for (var data in datas) {
      File file = File(data.getVideoPath());
      if (await file.exists()) {
        await file.delete();
      }
    }
  }

  Future<void> deleteSurgeryFolder(List<OfflineSurgeryData> datas) async {
    for (var data in datas) {
      try {
        final dir = Directory(data.getSurgeryDir());
        if (await dir.exists()) {
          await dir.delete(recursive: true);
        }
      } on FileSystemException catch (e) {
        app.logE('文件系统错误: ${e.message}');
      } catch (e) {
        app.logE('手术删除错误: $e');
      }
    }
  }
}
