import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:isar/isar.dart';
import 'package:surgsmart/src/apis/http/insight.api.dart';
import 'package:surgsmart/src/apis/http/surgery.api.dart';
import 'package:surgsmart/src/apis/http/tdengine.api.dart';
import 'package:surgsmart/src/apis/http/video.api.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/cos.model.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:surgsmart/src/models/mqtt/file.model.dart';
import 'package:surgsmart/src/modules/file/file/file.controller.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/db_utils.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/event_bus.dart';
import 'package:surgsmart/src/tools/throttle_control.dart';
import 'package:surgsmart/src/tools/upload_manager.dart';

class DataSyncManager {
  DataSyncManager._();

  static final instance = DataSyncManager._();
  factory DataSyncManager() => instance;

  ///离线数据记录同步队列
  final Queue<OfflineSurgeryData> _surgeryQueue = Queue();

  ///定时任务，实时视频片段上传、检测合并、过期删除
  Timer? syncTimer;

  ///当前正在同步的离线数据记录
  OfflineSurgeryData? _currentTask;
  OfflineSurgeryData? get currentTask => _currentTask;

  ///确保同一时间只有一个视频上传
  bool isSyncing = false;

  ///是否停止同步
  bool _stopSync = false;

  ///确保同一时间只有一个视频合并
  bool videoMerging = false;

  ///用于总同步进度估算
  int totalVideoDataSize = 0;
  int uploadedVideoDataSize = 0;
  int lastSecondUploadedVideoDataSize = 0;
  //上传数据队列缓存，最大存储10s, 用于预估上传剩余时间
  final Queue<int> _uploadDataBlockQueue = Queue();

  Future<void> startSyncTask() async {
    _stopSync = false;

    syncTimer ??= Timer.periodic(const Duration(seconds: 10), (timer) async {
      checkVideoMerge();
      if (DbUtils().currentSurgeryInfo != null &&
          !DbUtils().currentSurgeryInfo!.videoSyncing) {
        videoSync(DbUtils().currentSurgeryInfo!);
      }
      overdueCleanup();

      //同步状态检测
      if (isUnableSync) {
        resetSyncProgress();
        sendSyncProgress(forceSend: true);
      }
    });
    _surgeryQueue.clear();
    _surgeryQueue.addAll(await unSyncSurgeryRecords());
    app.logW('当前离线数据同步队列长度：${_surgeryQueue.length}');

    _autoSync();
    calculateTotalVideoSize();
  }

  void stopSyncVideo() {
    _currentTask = null;
    isSyncing = false;
    _stopSync = true;
    //保留待上传队列，留存出错的记录
    //_surgeryQueue.clear();
    UploadManager().cancelUploading();
  }

  Future<void> addSyncTask(
    OfflineSurgeryData task, {
    bool priority = false,
  }) async {
    if (task.endTime == null) return;
    app.logW('当前离线数据同步队列长度：${_surgeryQueue.length}');
    if (priority) {
      _surgeryQueue.addFirst(task);
    } else {
      _surgeryQueue.addLast(task);
    }
    _autoSync();
    calculateTotalVideoSize();
  }

  void removeSyncTask(List<OfflineSurgeryData> tasks) async {
    //通过id移除
    for (var task in tasks) {
      bool exist = _surgeryQueue.any(
        (surgery) => surgery.localSurgeryId == task.localSurgeryId,
      );
      if (exist) {
        _surgeryQueue.removeWhere(
          (surgery) => surgery.localSurgeryId == task.localSurgeryId,
        );
        calculateTotalVideoSize(removedSurgery: task);
      }
    }
  }

  /// 获取待同步的手术列表
  List<OfflineSurgeryData> get awaitSyncSurgeryList => _surgeryQueue.toList();

  /// 列表是否暂时无法同步,确保极端情况下依然可以检测更新
  bool get isUnableSync =>
      _surgeryQueue.every((surgery) => surgery.errCount >= 3);

  Future<void> onError(OfflineSurgeryData task) async {
    removeSyncTask([task]);
    if (task.errCount < 3) {
      task.errCount++;
      task.videoSyncing = false;
      _surgeryQueue.addLast(task);
    }
    _currentTask = null;
    isSyncing = false;
    _autoSync();
  }

  Future<List<OfflineSurgeryData>> unSyncSurgeryRecords() async {
    return DbUtils().isar.offlineSurgeryDatas
        .filter()
        .group(
          (q) => q
              .remoteSurgeryIdIsNull()
              .or()
              .notifyReportCreatedEqualTo(false)
              .or()
              .videoSyncedEqualTo(false)
              .or()
              .markUploadedEqualTo(false),
        )
        .endTimeIsNotNull()
        .recordStatusEqualTo(0)
        .sortByCreateTimeDesc()
        .findAll();
  }

  Future<List<OfflineSurgeryData>> localVideoUnMerge() async {
    return DbUtils().isar.offlineSurgeryDatas
        .filter()
        .localVideoMergedEqualTo(false)
        .endTimeIsNotNull()
        .recordStatusEqualTo(0)
        .sortByCreateTimeDesc()
        .findAll();
  }

  Future<void> _autoSync() async {
    if (_surgeryQueue.isEmpty) {
      isSyncing = false;
      return;
    }
    if (isSyncing) return;
    isSyncing = true;
    _currentTask = _surgeryQueue.first;
    try {
      // 检查远端手术id
      if (!await createRemoteSurgery(_currentTask!)) return;

      // 检查报告
      await notifyReportCreate(_currentTask!);

      // 检查睿标记
      await notifyMarkUpload(_currentTask!);

      // 检查视频
      await videoSync(_currentTask!);

      // 检查是否完成
      if (_currentTask!.remoteSurgeryId != null &&
          _currentTask!.notifyReportCreated &&
          _currentTask!.markUploaded != false &&
          _currentTask!.videoSynced) {
        removeSyncTask([_currentTask!]);
        _currentTask = null;
        isSyncing = false;
        _autoSync();
      }
    } catch (e) {
      app.logE('自动同步失败${e.toString()}');
      onError(_currentTask!);
    }
  }

  Future<void> videoSync(OfflineSurgeryData task) async {
    if (!AppContext.share.networkConnected.value ||
        task.videoSynced ||
        task.videoSyncing ||
        _stopSync) {
      return;
    }

    if (task.remoteSurgeryId == null) {
      if (!await createRemoteSurgery(task)) return;
    }
    //获取待上传视频片段列表
    SplayTreeMap<int, String> videoMap = await getUnUploadedVideos(task);

    if (videoMap.isEmpty && task.endTime == null) return;
    int uploadCount = 0;

    String? videoKey = await HttpVideoApi.getVideoKey(
      surgeryId: task.remoteSurgeryId!,
    );
    if (videoKey == null) {
      //此处videoKey == null，表示远端手术不存在，删除本地数据
      await DbUtils().softDeleteSurgery([task]);
      DataSyncManager().removeSyncTask([task]);
      return;
    }

    app.logW("videoSync videoKey $videoKey");

    task.videoSyncing = true;

    for (MapEntry<int, String> entry in videoMap.entries) {
      int index = entry.key;
      String path = entry.value;
      try {
        dynamic video = await HttpVideoApi.getOrCreateVideo(
          surgeryId: task.remoteSurgeryId!,
          videoKey: videoKey,
        );
        app.logW("videoSync getOrCreateVideo ${video.toString()}");

        dynamic segment = await HttpVideoApi.getOrAddSegment(
          segments: video["segments"],
          filePath: path,
          videoKey: videoKey,
          order: index,
        );
        app.logW("videoSync getOrAddSegment ${segment.toString()}");

        if (segment['id'] != null) {
          var res = await HttpVideoApi.setSegmentStatus(
            videoKey: videoKey,
            segmentId: segment["id"],
            status: "uploading",
          );
          app.logW("videoSync setSegmentStatus uploading ${res.toString()}");
        }

        // s3地址put上传
        ApiModel uploadUrls =
            await HttpSurgeryApi<ApiModel>.getVideoUploadUrls(
              videoKey: videoKey,
              fileNames: [getFileNameByPath(path)],
            ).request();
        String? putUrl = uploadUrls.map?['upload_urls']?.first;
        app.logI("videoSync put url: $putUrl");
        if (putUrl == null) continue;
        int increment = 0;
        if (!await UploadManager().uploadFile(
          UploadTask(
            url: putUrl,
            filePath: path,
            onProgress: (count, total) {
              uploadedVideoDataSize += (count - increment);
              increment = count;

              sendSyncProgress();
            },
            onError: (e) {
              if (_stopSync ||
                  (task.endTime != null && index == videoMap.lastKey())) {
                throw e;
              }
            },
          ),
        )) {
          continue;
        }

        app.logW("videoSync putFileObject uploaded");

        if (segment['id'] != null) {
          var res = await HttpVideoApi.setSegmentStatus(
            videoKey: videoKey,
            segmentId: segment["id"],
            status: "uploaded",
          );
          app.logW(
            "videoSync setSegmentStatus uploaded  videoKey:$videoKey，segmentId：${segment["id"]} ${res.toString()}",
          );
        }
        await File(
          '${task.getRecordedDir()}uploaded',
        ).writeAsString('${index.toString()}\n', mode: FileMode.append);
        uploadCount++;
      } catch (e) {
        app.logE("videoSync error ${e.toString()}");
        if (task.endTime != null && index == videoMap.lastKey()) {
          rethrow;
        }
      }
    }
    task.videoSyncing = false;

    if (uploadCount == videoMap.length && task.endTime != null) {
      //通知合并
      var res = await HttpVideoApi.allowMerge(videoKey: videoKey);
      if (task.endTime != null) {
        task.videoSynced = true;
        DbUtils().put([task]);
      }
      app.logW("videoSync allowMerge ${res.toString()}");
    }
  }

  Future<void> notifyReportCreate(OfflineSurgeryData task) async {
    if (task.notifyReportCreated) return;
    if (!AppContext.share.networkConnected.value) return;

    // 检查远端手术id
    if (!await createRemoteSurgery(task)) return;

    // 上传视频帧数据
    String? videoFramePath = await createSurgeryVideoInfoFile(task);
    if (videoFramePath == null) {
      throw 'videoFramePath == null';
    }

    String? videoFrameCosPath = await s3UploadByType(
      presignType: PresignType.videoFrame,
      filePath: videoFramePath,
      remoteSurgeryId: task.remoteSurgeryId!,
    );
    app.logW(
      'offline createSurgeryVideoInfoFile  $videoFramePath : $videoFrameCosPath',
    );
    if (videoFrameCosPath == null) {
      throw 'videoFrameCosPath == null';
    }

    // 上传AI数据
    String? videoAiPath = await HttpTDEngineApi.createSurgeryAiInfoFile(task);

    if (videoAiPath == null) {
      throw 'videoAiPath == null';
    }
    String? videoAiCosPath = await s3UploadByType(
      presignType: PresignType.ai,
      filePath: videoAiPath,
      remoteSurgeryId: task.remoteSurgeryId!,
    );

    app.logW(
      'offline createSurgeryVideoInfoFile $videoAiPath : $videoAiCosPath',
    );

    if (videoAiCosPath == null) {
      throw 'videoAiCosPath == null';
    }

    // 通知报告生成
    await HttpInsightApi.uploadRawCallback(
      surgeryId: task.remoteSurgeryId!,
      aiCosKey: '${task.remoteSurgeryId!}-raw.json',
    ).request().then((data) async {
      task.notifyReportCreated = true;
      await DbUtils().put([task]);
      app.logW('offline uploadRawCallback success');
    });
  }

  Future<void> notifyMarkUpload(OfflineSurgeryData task) async {
    if (task.markUploaded != false) return;
    if (!AppContext.share.networkConnected.value) return;

    // 检查远端手术id
    if (!await createRemoteSurgery(task)) return;

    // todo 上传标记数据
    String screenshots =
        '${AppContext.share.documentsPath}/v202310/${task.localSurgeryId}/screenshots/';
    final directory = Directory(screenshots);
    if (!directory.existsSync()) return;
    List<FileSystemEntity> files = directory.listSync(followLinks: false);
    for (var fileSystemEntity in files) {
      if (fileSystemEntity is File) {
        String? cosUrl = await s3UploadByType(
          presignType: PresignType.quick,
          filePath: fileSystemEntity.path,
          remoteSurgeryId: task.remoteSurgeryId!,
        );
        if (cosUrl == null) {
          throw 'quickCosUrl == null';
        }

        int index = fileSystemEntity.path.lastIndexOf('/');
        if (index == -1 || !fileSystemEntity.path.endsWith('.png')) return;
        String name = fileSystemEntity.path.substring(
          index + 1,
          fileSystemEntity.path.length - 4,
        );
        SurgeryMarkTimeInfo markInfo =
            await HttpSurgeryApi<SurgeryMarkTimeInfo>.mark(
              surgeryId: task.remoteSurgeryId!,
              timePoint: int.parse(name),
              imageStorageUrl: cosUrl,
            ).request();
        if (markInfo.id != 0) {
          app.logW("mark上传成功${fileSystemEntity.path}");
          await fileSystemEntity.delete();
        }
      }
    }

    if (files.isNotEmpty && directory.listSync(followLinks: false).isEmpty) {
      task.markUploaded = true;
      await DbUtils().put([task]);
    }
  }

  Future<String?> s3UploadByType({
    required PresignType presignType,
    required int remoteSurgeryId,
    required String filePath,
  }) async {
    S3UploadCredential uploadUrls =
        await HttpSurgeryApi<S3UploadCredential>.getUploadUrl(
          surgeryId: remoteSurgeryId,
          presignType: presignType,
        ).request();
    app.logI(
      "presignType: ${presignType.value}, put url: ${uploadUrls.presignUrl}",
    );
    if (!await UploadManager().uploadFile(
      UploadTask(url: uploadUrls.presignUrl, filePath: filePath),
    )) {
      return null;
    }
    return uploadUrls.storageUrl;
  }

  Future<void> checkVideoMerge() async {
    // 按手术时间倒序查询未合并视频，依次合并
    if (videoMerging) return;
    List<OfflineSurgeryData> surgers = await localVideoUnMerge();
    if (surgers.isEmpty) return;
    videoMerging = true;
    for (var surgery in surgers) {
      try {
        await mergeVideo(surgery);
      } catch (e) {
        app.logE("offline 合并异常：${e.toString()}");
      }
    }
    calculateTotalVideoSize();
    videoMerging = false;
  }

  // 合并本地视频
  Future<void> mergeVideo(OfflineSurgeryData surgery) async {
    final directory = Directory(surgery.getRecordedDir());
    if (!directory.existsSync()) return;
    final SplayTreeMap<int, String> videoMap = SplayTreeMap<int, String>();
    RegExp pattern = RegExp(r'^\d+_(\d+)\.mp4$');
    directory.listSync(followLinks: false).forEach((fileSystemEntity) {
      String name = getFileNameByPath(fileSystemEntity.path);
      final match = pattern.firstMatch(name);
      final index = match?.group(1);
      if (index == null) return;
      videoMap[int.parse(index)] = "file '${fileSystemEntity.path}'";
    });
    if (videoMap.values.isEmpty) return;
    String fileList = '${surgery.getRecordedDir()}fileList.txt';
    StringBuffer buffer = StringBuffer();
    for (var element in videoMap.values) {
      buffer.writeln(element);
    }
    File(fileList).writeAsStringSync(buffer.toString());

    bool res = await DeviceCmd.share.mergeVideo(
      fileList: fileList,
      outputPath:
          '${AppContext.share.documentsPath}/v202310/merged/${surgery.localSurgeryId}.mp4',
    );

    if (res) {
      surgery.localVideoMerged = true;
      DbUtils().put([surgery]);
    }
  }

  Future<bool> createRemoteSurgery(OfflineSurgeryData task) async {
    if (task.remoteSurgeryId != null) return true;
    if (!AppContext.share.networkConnected.value) return false;
    try {
      SurgeryInfo surgeryInfo =
          await HttpSurgeryApi<SurgeryInfo>.create(
            userId: task.surgeonId,
            procedureName: task.readProcedure()?.name ?? "",
            admissionNumber: task.admissionNumber,
            department: task.readDepartment()?.name ?? "",
          ).request();
      task.remoteSurgeryId = surgeryInfo.id;
      await DbUtils().put([task]);
      app.logW("offline createRemoteSurgery success: ${surgeryInfo.id}");

      return true;
    } catch (e) {
      app.logE("offline createRemoteSurgery error: $e");
      return false;
    }
  }

  /// 创建手术视频帧文件
  Future<String?> createSurgeryVideoInfoFile(OfflineSurgeryData task) async {
    try {
      String surgeryDir =
          '${AppContext.share.documentsPath}/v202310/${task.localSurgeryId}/';
      String path = '$surgeryDir/${task.remoteSurgeryId}.json';
      File videoFrameFile = File(path);
      if (videoFrameFile.existsSync()) return path;

      File streamList = File('$surgeryDir/stream_list');
      File videoInfo = File('$surgeryDir/video_info.txt');
      if (!videoInfo.existsSync()) return null;

      //流式读取避免文件过大
      List<int> frameTimestamps = [];
      if (streamList.existsSync()) {
        await streamList
            .openRead()
            .transform(utf8.decoder) // 转换为 UTF-8 字符串
            .transform(LineSplitter())
            .listen((line) async {
              final value = int.tryParse(line.substring(0, line.indexOf('_')));
              if (value != null) {
                frameTimestamps.add(value);
              }
            })
            .asFuture();
      }
      if (frameTimestamps.length < 30 && task.isNoSignalAll()) {
        frameTimestamps = [];
      }

      String videoMeta = await videoInfo.readAsString();
      String videoInfoJson = '''
{"frame_timestamps": ${frameTimestamps.toString()}, "meta": $videoMeta}
''';
      await videoFrameFile.writeAsString(videoInfoJson, flush: true);
      return path;
    } catch (e) {
      app.logE(e);
      return null;
    }
  }

  String getFileNameByPath(String path) {
    return path.substring(path.lastIndexOf('/') + 1);
  }

  Future<void> videoRepair(Directory directory) async {
    //修复info文件
    await DeviceCmd.share.videoInfoRepair(directory.path);

    List<FileSystemEntity> fileList = directory.listSync(followLinks: false);
    String? workingVideo;

    OfflineSurgeryData? surgeryData =
        await DbUtils().queryRecentSurgeryRecord();
    if (surgeryData != null) {
      workingVideo = getNormalVideo(Directory(surgeryData.getRecordedDir()));
    }

    for (int i = 0; i < fileList.length; i++) {
      FileSystemEntity file = fileList[i];
      if (file is! File) return;
      if (file.path.endsWith(".bad.mp4")) {
        workingVideo ??= getNormalVideo(directory);
        await DeviceCmd.share.badVideoRepair(
          workingVideo ?? file.path,
          file.path,
        );
        File fixedFile = File("${file.path}_fixed.mp4");
        if (fixedFile.existsSync()) {
          fixedFile.renameSync(
            fixedFile.path.replaceAll(".bad.mp4_fixed.mp4", '.mp4'),
          );
          //删除bad文件避免重复修复
          await file.delete();
        }
      }
    }
  }

  /// 获取正常视频作为修复参考
  String? getNormalVideo(Directory directory) {
    if (!directory.existsSync()) return null;
    List<FileSystemEntity> fileList = directory.listSync(followLinks: false);
    for (FileSystemEntity file in fileList) {
      if (file is! File) continue;
      RegExp pattern = RegExp(r'^\d+_(\d+)\.mp4$');
      if (pattern.hasMatch(getFileNameByPath(file.path))) {
        return file.path;
      }
    }
    return null;
  }

  //清理过期数据
  Future<void> overdueCleanup() async {
    if (!Throttle().checkPass('overdueCleanup', intervalMs: 1000 * 60)) return;
    List<OfflineSurgeryData> datas = await DbUtils().queryDaysAgoSurgeryRecord(
      7,
    );
    if (datas.isEmpty) return;
    await DbUtils().softDeleteSurgery(datas);
    removeSyncTask(datas);
    FileController.deleteMergedVideo(datas);
  }

  ///计算上传总进度
  Future<void> calculateTotalVideoSize({
    OfflineSurgeryData? removedSurgery,
  }) async {
    if (removedSurgery != null) {
      /// 移除待上传队列中的任务，则减少上传总数据量
      if (!_stopSync &&
          removedSurgery.localSurgeryId != currentTask?.localSurgeryId) {
        int videoSize = await removedSurgery.getVideoSize() ?? 0;
        if (totalVideoDataSize > videoSize) {
          totalVideoDataSize -= videoSize;
        }
      }
    } else {
      int totalSize = 0;
      // todo 迭代修改问题？
      for (var surgery in _surgeryQueue) {
        if (surgery.localSurgeryId == _surgeryQueue.first.localSurgeryId) {
          File uploadedRecord = File('${surgery.getRecordedDir()}uploaded');
          //正在上传的手术，计算待上传片段
          if (uploadedRecord.existsSync()) {
            SplayTreeMap<int, String> videos = await getUnUploadedVideos(
              _surgeryQueue.first,
            );
            for (var path in videos.values) {
              totalSize += await surgery.getVideoSize(path: path) ?? 0;
            }
            continue;
          }
        }
        totalSize += (await surgery.getVideoSize() ?? 0);
      }
      resetSyncProgress();
      //累加完成后再赋值，保证预估时间准确性
      totalVideoDataSize = totalSize;
    }
  }

  /// 发送预估进度
  void sendSyncProgress({bool forceSend = false}) {
    if (forceSend ||
        Throttle().checkPass('sendSyncProgress', intervalMs: 1000)) {
      double progress = 1.0;
      int predicatedUploadTime = 0;

      if (totalVideoDataSize != 0 && uploadedVideoDataSize != 0) {
        progress = uploadedVideoDataSize / totalVideoDataSize;
        if (progress >= 0.99) {
          progress = 0.99;
        }

        _uploadDataBlockQueue.add(
          uploadedVideoDataSize - lastSecondUploadedVideoDataSize,
        );

        if (_uploadDataBlockQueue.length > 10) {
          _uploadDataBlockQueue.removeFirst();
        }

        float dataSizeOfSecond =
            _uploadDataBlockQueue.fold(0, (sum, size) => sum + size) /
            _uploadDataBlockQueue.length;

        if (dataSizeOfSecond <= 0) return;
        predicatedUploadTime =
            (totalVideoDataSize - uploadedVideoDataSize) ~/ dataSizeOfSecond;

        lastSecondUploadedVideoDataSize = uploadedVideoDataSize;
        if (predicatedUploadTime < 3) predicatedUploadTime = 3;
      }

      app.logW("发送同步進度------$progress,$predicatedUploadTime");

      eventBus.fire(
        EventBusInfo<AllFileStatsInfo>(
          type: EventBusType.videoSyncProgress,
          data: AllFileStatsInfo().initWith({
            'progress': progress,
            'predicatedUploadTime': predicatedUploadTime,
          }),
        ),
      );
    }
  }

  void resetSyncProgress() {
    uploadedVideoDataSize = 0;
    totalVideoDataSize = 0;
    lastSecondUploadedVideoDataSize = 0;
  }

  Future<SplayTreeMap<int, String>> getUnUploadedVideos(
    OfflineSurgeryData task,
  ) async {
    final SplayTreeMap<int, String> videoMap = SplayTreeMap<int, String>();

    final directory = Directory(task.getRecordedDir());
    if (!directory.existsSync() || task.isNoSignalAll()) return videoMap;
    // 正在生成的bad文件不修复
    if (task.endTime != null) {
      await videoRepair(directory);
    }

    File uploadedRecord = File('${task.getRecordedDir()}uploaded');
    List<int> uploadedList = [];
    if (uploadedRecord.existsSync()) {
      uploadedRecord.readAsLinesSync().forEach((i) {
        int? index = int.tryParse(i);
        if (index != null) {
          uploadedList.add(index);
        }
      });
    }

    RegExp pattern = RegExp(r'^\d+_(\d+)\.mp4$');
    List<FileSystemEntity> fileList = directory.listSync(followLinks: false);
    for (FileSystemEntity fileSystemEntity in fileList) {
      final match = pattern.firstMatch(
        getFileNameByPath(fileSystemEntity.path),
      );
      final index = match?.group(1);
      if (index == null) continue;
      FileStat fileStat = await fileSystemEntity.stat();
      if (fileStat.size == 229) continue; //按原离线服务逻辑判定为无视频流的文件
      int videoIndex = int.parse(index);
      if (!uploadedList.contains(videoIndex)) {
        videoMap[videoIndex] = fileSystemEntity.path;
      }
    }
    return videoMap;
  }
}
