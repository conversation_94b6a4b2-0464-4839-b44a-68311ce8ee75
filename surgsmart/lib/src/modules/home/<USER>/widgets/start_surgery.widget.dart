import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/http/file_statistics.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/tools/throttle_control.dart';
import 'package:surgsmart/src/widgets/click_animation.widget.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';
import 'package:surgsmart/src/widgets/text_span.widget.dart';

class StartSurgery extends StatefulWidget {
  const StartSurgery({
    super.key,
    required this.surgeryCount,
    required this.spaceInfo,
  });

  final SurgeryStatisticInfo surgeryCount;
  final ffi.HostDeviceSpaceInfo spaceInfo;

  @override
  State<StartSurgery> createState() => _StartSurgeryState();
}

class _StartSurgeryState extends State<StartSurgery> {
  bool diskFull = false;

  @override
  Widget build(BuildContext context) {
    String surgeryData = StringUtil.replaceInterpolation(
      S.current.f2_43uAUnLU,
      [
        widget.surgeryCount.surgeryCount, //DbUtils.instance.queryCount()
        formatTimestamp(
          widget.surgeryCount.surgeryDuration,
        ), //AppPreferences.surgeryHistoryDuration.intValue
      ],
    );
    return ClickAnimateContainer(
      onTap: () {
        if (!Throttle.instance.checkPass("surgeryCreate")) {
          return;
        }
        int resolution = AppPreferences.resolution.intValue ?? 1080;
        double availableRecordingTime = 1;
        if (resolution == 2160) {
          availableRecordingTime =
              widget.spaceInfo.available * 8 / (24 * 1024 * 1024 * 3600);
        } else {
          availableRecordingTime =
              widget.spaceInfo.available * 8 / (6 * 1024 * 1024 * 3600);
        }
        if (availableRecordingTime < 10) {
          showDiskFull(context);
        } else {
          app.openInner(GoPaths.surgeryCreate);
        }
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: 64.w),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF374762), Color(0xff25456d)],
          ),
          image: DecorationImage(
            image: R.image.surgery_creation_background(),
            fit: BoxFit.fill,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xffB5D7F3).withValues(alpha: 0.54),
              offset: const Offset(0, 0),
              blurRadius: 32.w,
            ),
          ],
          border: Border.all(
            color: const Color(0xffB5D7F3).withValues(alpha: 0.54),
            width: 4.w,
          ),
          borderRadius: BorderRadius.all(Radius.circular(32.w)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 120.h),
            Flexible(
              child: Image(
                image: R.image.create_surgery(),
                height: double.infinity,
                fit: BoxFit.fitHeight,
              ),
            ),
            SizedBox(height: 20.h),
            Text(S.current.f_43uAY8UG, style: TextStyle(fontSize: 96.sp)),
            SizedBox(height: 20.h),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextSpanWidget(
                  text: surgeryData,
                  defaultStyle: TextStyle(
                    fontSize: 64.sp,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                  descriptions:
                      widget.surgeryCount.surgeryCount == 0
                          ? []
                          : [
                            TextSpanDescription(
                              startIndex: surgeryData.indexOf(
                                widget.surgeryCount.surgeryCount.toString(),
                              ),
                              spanLength:
                                  widget.surgeryCount.surgeryCount
                                      .toString()
                                      .length, // todo 多语言插值位置可能不同，故此处length需和插值对应，目前仅一处且插值顺序相同，后续优化
                              style: TextStyle(
                                fontSize: 64.sp,
                                color: const Color(0xff23D9BD),
                              ),
                            ),
                            TextSpanDescription(
                              startIndex: surgeryData.lastIndexOf(
                                formatTimestamp(
                                  widget.surgeryCount.surgeryDuration,
                                ),
                              ),
                              spanLength:
                                  formatTimestamp(
                                    widget.surgeryCount.surgeryDuration,
                                  ).length,
                              style: TextStyle(
                                fontSize: 64.sp,
                                color: const Color(0xff23D9BD),
                              ),
                            ),
                          ],
                ),
              ],
            ),
            SizedBox(height: 50.h),
          ],
        ),
      ),
    );
  }

  /// 开始手术弹窗提示-本地缓存已满
  void showDiskFull(BuildContext context) {
    Modal(
      title: S.current.f_MuNPiOOD,
      kind: ModalKind.dialog,
      type: ModalType.error,
      cancelText: S.current.f_7j1BDkim,
      confirmText: S.current.f_MwxsL4YA,
      message: S.current.f_Mwxs8yVv,
      onConfirm: () async {
        context.pop();
        app.openInner(GoPaths.file);
      },
      onClose: () {
        context.pop();
      },
    ).show(context, barrierDismissible: false);
  }

  /// 时间换算
  String formatTimestamp(int ms) {
    var hours = (ms / 3600 / 1000).floor();
    return '$hours';
  }
}
