import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/algorithm/widgets/detector_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/algorithm/widgets/score_card.widget.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';

import 'algorithm.controller.dart';

/// 所属模块: surgery
///
/// 所属路由: surgeryRoom
///
/// 算法数据面板
class AlgorithmView extends AppView<AlgorithmController> {
  const AlgorithmView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return controller.build(
      onSuccess: onSuccess,
      onFailure: onFailure,
      onLoading: onLoading,
      onEmpty: onEmpty,
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    final detectorCard = ValueListenableBuilder(
      valueListenable: controller.surgeryEvent,
      builder: (context, value, child) {
        return DetectorCard(eventCounts: value, controller: controller);
      },
    );

    final card = ValueListenableBuilder(
      valueListenable: controller.cvs,
      builder: (context, value, child) {
        return value.status == CvsStatus.pending ||
                value.status == CvsStatus.expired
            ? detectorCard
            : ScoreCard(
              cvs1: value.cvs1.toInt(),
              cvs2: value.cvs2.toInt(),
              cvs3: value.cvs3.toInt(),
              cvsAverage:
                  value.status == CvsStatus.ongoing
                      ? null
                      : (value.cvs1 + value.cvs2 + value.cvs3) ~/ 3,
            );
      },
    );
    return AppBackground(
      child: Column(
        children: [
          SizedBox(height: 30.h),
          Expanded(child: card),
          Container(
            width: double.infinity,
            height: 172.h,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF2A5977), Color(0xFF1E726C)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(30.r),
            ),
            padding: EdgeInsets.symmetric(horizontal: 25.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ValueListenableBuilder(
                  valueListenable:
                      controller
                          .parent<SurgeryRoomController>()
                          ?.firstSignalRecover ??
                      false.notifier,
                  builder: (context, firstSignalRecover, child) {
                    return ValueListenableBuilder(
                      valueListenable: controller.currentPhaseId,
                      builder: (context, currentPhaseId, child) {
                        return Transform.translate(
                          offset: Offset(0, -9.5.h),
                          child: Text(
                            getCurrentDesc(currentPhaseId),
                            style: TextStyle(
                              fontSize: 80.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                              height: 1.0,
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
                ValueListenableBuilder(
                  valueListenable: controller.currentPhaseDuration,
                  builder:
                      (context, currentPhaseDuration, child) => Visibility(
                        visible:
                            getCurrentDuration(currentPhaseDuration).isNotEmpty,
                        child: Expanded(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                getCurrentDuration(currentPhaseDuration),
                                style: GoogleFonts.roboto(
                                  shadows: [
                                    Shadow(
                                      blurRadius: 4,
                                      color: Colors.black.withValues(
                                        alpha: 0.5,
                                      ),
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                  textStyle: TextStyle(
                                    color: Colors.white,
                                    fontSize: 96.sp,
                                    fontWeight: FontWeight.bold,
                                    height: 1.0,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  String getCurrentDesc(currentPhaseId) {
    if (controller.parent<SurgeryRoomController>()?.noSignalAll() == true) {
      return S.current.f_IB6hPSwo;
    }
    if (currentPhaseId == null) {
      return S.current.f_8VV63xRn;
    }

    if (currentPhaseId == 1210111000 || currentPhaseId == 1310111000) {
      return S.current.f_94737O1Y;
    }

    if (Intl.getCurrentLocale() == 'zh_CN') {
      return controller.insightFullMap[currentPhaseId]["feature"]['label'];
    } else {
      String name =
          controller.insightFullMap[currentPhaseId]["feature"]['name'];
      return StringUtil.firstUpperCase(name) ?? "";
    }
  }

  String getCurrentDuration(int? currentPhaseDuration) {
    if (currentPhaseDuration == null ||
        controller.currentPhaseId.value == 1210111000 ||
        controller.currentPhaseId.value == 1310111000 ||
        controller.parent<SurgeryRoomController>()?.noSignalAll() == true) {
      return "";
    }
    return currentPhaseDuration.toHMS;
  }
}
