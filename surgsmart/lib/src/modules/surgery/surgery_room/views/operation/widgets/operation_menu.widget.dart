import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/apis/app_config.dart';

enum OperationState {
  function,
  rtcLive,
  rtmLive,
  control,
  setting,
}

class OperationMenu extends StatefulWidget {
  final OperationState operationState;
  final void Function(OperationState operationState) onTap;

  const OperationMenu({
    super.key,
    required this.operationState,
    required this.onTap,
  });

  @override
  State<OperationMenu> createState() => _OperationMenuState();
}

class _OperationMenuState extends State<OperationMenu> {
  @override
  Widget build(BuildContext context) {
    final decoration = BoxDecoration(
      gradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Color(0xFF00B0BF),
          Color(0xFF025693),
        ],
      ),
      borderRadius: BorderRadius.circular(57.r),
    );
    return Column(
      children: [
        SizedBox(height: 35.h),
        <PERSON><PERSON>(
          onPressed: () {
            widget.onTap(OperationState.function);
          },
          borderRadius: BorderRadius.circular(57.r),
          label: Container(
            decoration: widget.operationState == OperationState.function
                ? decoration
                : null,
            alignment: Alignment.center,
            width: 114.r,
            height: 114.r,
            child: Image(
              image: R.image.logo_only(),
              fit: BoxFit.contain,
              width: 80.r,
              height: 80.r,
            ),
          ),
        ),
        SizedBox(height: 35.h),
        Button(
          onPressed: () {
            widget.onTap(OperationState.rtcLive);
          },
          borderRadius: BorderRadius.circular(57.r),
          label: Container(
            decoration: widget.operationState == OperationState.rtcLive
                ? decoration
                : null,
            alignment: Alignment.center,
            width: 114.r,
            height: 114.r,
            child: Image(
              image: R.image.rtc_live(),
              fit: BoxFit.contain,
              width: 72.r,
              height: 72.r,
            ),
          ),
        ),
        SizedBox(height: 35.h),
        if (AppConfig.config.featureFlags.surgeryLive)
          Button(
          onPressed: () {
            widget.onTap(OperationState.rtmLive);
          },
          borderRadius: BorderRadius.circular(57.r),
          label: Container(
            decoration: widget.operationState == OperationState.rtmLive
                ? decoration
                : null,
            alignment: Alignment.center,
            width: 114.r,
            height: 114.r,
            child: Image(
              image: R.image.rtm_live(),
              fit: BoxFit.contain,
              width: 80.r,
              height: 80.r,
            ),
          ),
        ),
        const Spacer(),
        Button(
          borderRadius: BorderRadius.circular(57.r),
          onPressed: () {
            widget.onTap(OperationState.control);
          },
          label: Container(
            decoration: widget.operationState == OperationState.control
                ? decoration
                : null,
            alignment: Alignment.center,
            width: 114.r,
            height: 114.r,
            child: Image(
              image: R.image.remote_control(),
              fit: BoxFit.contain,
              width: 72.r,
              height: 72.r,
            ),
          ),
        ),
        SizedBox(height: 35.h),
        Button(
          onPressed: () {
            widget.onTap(OperationState.setting);
          },
          borderRadius: BorderRadius.circular(57.r),
          label: Container(
            decoration: widget.operationState == OperationState.setting
                ? decoration
                : null,
            alignment: Alignment.center,
            width: 114.r,
            height: 114.r,
            child: Image(
              image: R.image.system_setting(),
              fit: BoxFit.contain,
              width: 72.r,
              height: 72.r,
            ),
          ),
        ),
        SizedBox(height: 35.h),
      ],
    );
  }
}
