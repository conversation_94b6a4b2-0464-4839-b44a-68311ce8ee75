import 'dart:async';
import 'package:app_foundation/app_foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/widgets/no_network.widget.dart';

enum RtcLiveCardType {
  users,
  qrcode,
  newJoin,
  speaking,
}

class RtcLiveCard extends StatefulWidget {
  final String? url;

  final List<DoctorInfo> doctors;
  final ValueChanged<bool> onMicrophoneChange;
  final bool isNetwork;

  const RtcLiveCard({
    super.key,
    required this.url,
    required this.doctors,
    required this.isNetwork,
    required this.onMicrophoneChange,
  });

  @override
  State<RtcLiveCard> createState() => _RtcLiveCardState();
}

class _RtcLiveCardState extends State<RtcLiveCard> {
  var type = RtcLiveCardType.users;
  DoctorInfo? currentDoctor;

  Timer? newJoinTimer;

  @override
  void initState() {
    updateType();
    super.initState();
  }

  @override
  void dispose() {
    newJoinTimer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant RtcLiveCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isNetwork != oldWidget.isNetwork ||
        widget.doctors != oldWidget.doctors) {
      updateType();
    }
  }

  // 设置当前展示卡片
  void updateType() {
    if (widget.doctors.isEmpty) {
      type = RtcLiveCardType.qrcode;
    } else {
      final newDoctor = widget.doctors.firstWhere(
        (e) => e.isNewJoin || e.speaking,
        orElse: () => DoctorInfo().initWith({}),
      );
      if (newDoctor.id != -1) {
        currentDoctor = newDoctor;
        if (currentDoctor?.isNewJoin == true) {
          type = RtcLiveCardType.newJoin;
          currentDoctor?.isNewJoin = false;
          newJoinTimer?.cancel();
          newJoinTimer = Timer(const Duration(seconds: 10), () {
            newJoinTimer?.cancel();
            newJoinTimer = null;
            // 在该定时任务时间内，协同人员可能退出，需加一层判断
            if (mounted && widget.doctors.isNotEmpty) {
              type = RtcLiveCardType.users;
              setState(() {});
            }
          });
        } else {
          if (newJoinTimer == null) {
            type = RtcLiveCardType.speaking;
          } else {
            type = RtcLiveCardType.users;
          }
        }
      } else {
        if (newJoinTimer == null && type != RtcLiveCardType.qrcode) {
          type = RtcLiveCardType.users;
        }
      }
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _RtcLiveHeader(
          isNetwork: widget.isNetwork,
          count: widget.doctors.length,
          isShowQrcode: RtcLiveCardType.qrcode == type,
          onMicrophoneChange: widget.onMicrophoneChange,
          onQrImageAndListChanged: (isShowQrcode) {
            type =
                isShowQrcode ? RtcLiveCardType.qrcode : RtcLiveCardType.users;
            setState(() {});
          },
        ),
        if (widget.isNetwork)
          Expanded(
              child: switch (type) {
            RtcLiveCardType.users => _DoctorListCard(widget.doctors),
            RtcLiveCardType.qrcode => _RtcQrcode(url: widget.url),
            RtcLiveCardType.newJoin => _NewJoinCard(doctor: currentDoctor),
            RtcLiveCardType.speaking => _SpeakingCard(doctor: currentDoctor),
          }),
        if (!widget.isNetwork) const NoNetwork()
      ],
    );
  }
}

class _RtcQrcode extends StatelessWidget {
  final String? url;
  const _RtcQrcode({required this.url});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        url == null
            ? Container(
                width: 440.r,
                height: 440.r,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0x73FFFFFF),
                  ),
                  borderRadius: BorderRadius.circular(8.r),
                  image: DecorationImage(
                    image: R.image.qrcode_bg(),
                  ),
                ),
                child: Image.asset(
                  R.image.what_mask().assetName,
                  width: 200.r,
                  height: 200.r,
                  fit: BoxFit.contain,
                ),
              )
            : Stack(
                children: [
                  QrImageView(
                    data: url!,
                    size: 440.r,
                    padding: EdgeInsets.all(20.r),
                    backgroundColor: Colors.white,
                  ),
                  Positioned.fill(
                    child: Center(
                      child: Container(
                        width: 72.r,
                        height: 72.r,
                        padding: EdgeInsets.all(8.r),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: Image(
                          image: R.image.company_logo(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
        Text(
          S.current.f_9aUbhIre,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 72.sp,
            height: 100 / 72,
          ),
        ),
      ],
    );
  }
}

class _RtcLiveHeader extends StatelessWidget {
  final bool isNetwork;
  final int count;
  final bool isShowQrcode;
  final ValueChanged<bool> onMicrophoneChange;
  final ValueChanged<bool> onQrImageAndListChanged;

  const _RtcLiveHeader({
    this.isNetwork = true,
    required this.count,
    required this.isShowQrcode,
    required this.onMicrophoneChange,
    required this.onQrImageAndListChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AppControllerBuilder<SurgeryRoomController>(
          builder: (context, controller) {
            return Container(
              margin: EdgeInsets.only(top: !isNetwork ? 18.h : 0, left: 24.w),
              padding: EdgeInsets.all(14.r),
              decoration: BoxDecoration(
                color: Colors.white10,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  controller.setMicrophoneState(
                    enabled: !controller.microphoneIsOn.value,
                  );
                },
                child: ValueListenableBuilder(
                  valueListenable: controller!.microphoneIsOn,
                  builder: (context, value, child) => Image(
                    image: value
                        ? R.image.microphone_on()
                        : R.image.microphone_off(),
                    width: 96.r,
                    height: 96.r,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            );
          },
        ),
        if (isNetwork)
          Text.rich(
            TextSpan(
              text: S.current.f_9aUb9VyH,
              style: TextStyle(
                color: Colors.white,
                fontSize: 80.sp,
                fontWeight: FontWeight.w500,
                height: 100 / 80,
              ),
              children: [
                TextSpan(
                  text: "(",
                  style: TextStyle(
                    fontSize: 56.sp,
                    height: 100 / 56,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: "$count",
                  style: GoogleFonts.roboto(
                    textStyle: TextStyle(
                      fontSize: 56.sp,
                      height: 100 / 56,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                TextSpan(
                  text: ")",
                  style: TextStyle(
                    fontSize: 56.sp,
                    height: 100 / 56,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        if (isNetwork)
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              onQrImageAndListChanged(!isShowQrcode);
            },
            child: Image(
              image: isShowQrcode
                  ? R.image.show_rtc_members()
                  : R.image.show_rtc_qrcode(),
              width: 160.r,
              height: 160.r,
            ),
          ),
      ],
    );
  }
}

class _DoctorListCard extends StatelessWidget {
  final List<DoctorInfo> doctors;
  //组件内部记录轮播位置持久化
  static int _lastShowUserId = 0;
  _DoctorListCard(this.doctors) {
    int lastShowUserIndex = doctors.indexWhere((e) => e.id == _lastShowUserId);
    if (lastShowUserIndex == doctors.length - 1) {
      return;
    }
    if (lastShowUserIndex > 3 && lastShowUserIndex < doctors.length - 1) {
      List<DoctorInfo> users = doctors.sublist(lastShowUserIndex + 1);
      users.addAll(doctors.sublist(0, lastShowUserIndex + 1));
      doctors.clear();
      doctors.addAll(users);
    }
  }

  @override
  Widget build(BuildContext context) {
    final count = (doctors.length / 4.0).ceil();
    return Container(
      margin: EdgeInsets.only(
        left: 25.w,
        right: 25.w,
        bottom: 15.h,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.r),
        border: Border.all(color: const Color(0xFF979797)),
      ),
      clipBehavior: Clip.hardEdge,
      child: count >= 1
          ? CarouselSlider.builder(
              options: CarouselOptions(
                aspectRatio: 772 / 708,
                viewportFraction: 1,
                autoPlay: true,
                enableInfiniteScroll: count > 1,
                autoPlayInterval: 10.seconds,
              ),
              itemCount: count,
              itemBuilder: (context, topIndex, realIndex) {
                final int len;
                if ((topIndex + 1) * 4 <= doctors.length) {
                  len = 4;
                } else {
                  len = doctors.length - topIndex * 4;
                }
                int lastIndex = topIndex * 4 + len - 1 < doctors.length
                    ? topIndex * 4 + len - 1
                    : doctors.length - 1;
                _lastShowUserId = doctors[lastIndex].id;
                return ListView.separated(
                  padding: EdgeInsets.symmetric(
                    vertical: 24.h,
                    horizontal: 50.w,
                  ),
                  itemCount: len,
                  separatorBuilder: (BuildContext context, int index) {
                    return SizedBox(height: 25.h);
                  },
                  itemBuilder: (BuildContext context, int index) {
                    final doctor = doctors[index + topIndex * 4];
                    return _CardTile(
                      url: doctor.avatarUrl,
                      title: doctor.name,
                      desc: doctor.organization,
                      microphoneIsOn: doctor.isMicrophoneActive,
                    );
                  },
                );
              },
            )
          : Center(
              child: Text(
                S.current.f_Mzuz876N,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 48.sp,
                ),
              ),
            ),
    );
  }
}

class _CardTile extends StatelessWidget {
  final String url;

  final String title;

  final String desc;

  final bool microphoneIsOn;

  const _CardTile({
    required this.url,
    required this.title,
    required this.desc,
    required this.microphoneIsOn,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Stack(
          children: [
            Container(
              padding: EdgeInsets.all(8.r),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(73.r),
              ),
              child: CachedNetworkImage(
                imageUrl: url,
                imageBuilder: (context, imageProvider) {
                  return CircleAvatar(
                    radius: 65.r,
                    backgroundColor: const Color(0xFFD8D8D8),
                    backgroundImage: imageProvider,
                  );
                },
                placeholder: (context, url) {
                  return CircleAvatar(
                    radius: 65.r,
                    backgroundColor: const Color(0xFFD8D8D8),
                    backgroundImage: R.image.default_avatar(),
                  );
                },
                errorWidget: (context, url, error) {
                  return CircleAvatar(
                    radius: 65.r,
                    backgroundColor: const Color(0xFFD8D8D8),
                    backgroundImage: R.image.default_avatar(),
                  );
                },
                fit: BoxFit.cover,
              ),
            ),
            Positioned(
              right: 0,
              bottom: 8.h,
              child: Image(
                image: microphoneIsOn
                    ? R.image.doctor_unmuted()
                    : R.image.doctor_muted(),
                width: 48.r,
                height: 48.r,
              ),
            ),
          ],
        ),
        SizedBox(width: 25.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                maxLines: 1,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 56.sp,
                  height: 78 / 56,
                  fontWeight: FontWeight.w500,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                desc,
                maxLines: 1,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 48.sp,
                  height: 78 / 56,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _NewJoinCard extends StatelessWidget {
  const _NewJoinCard({this.doctor});

  final DoctorInfo? doctor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      margin: EdgeInsets.only(
        left: 25.w,
        right: 25.w,
        bottom: 15.h,
      ),
      padding: EdgeInsets.symmetric(
        vertical: 30.h,
        horizontal: 20.r,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.r),
        border: Border.all(color: const Color(0xFF979797)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            S.current.f_Mx22wA8j,
            style: TextStyle(
              color: Colors.white,
              fontSize: 64.sp,
              height: 90 / 64,
              fontWeight: FontWeight.w500,
            ),
          ),
          Container(
            padding: EdgeInsets.all(20.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(350.r),
            ),
            child: CachedNetworkImage(
              imageUrl: doctor!.avatarUrl,
              imageBuilder: (context, imageProvider) {
                return CircleAvatar(
                  radius: 156.r,
                  backgroundColor: const Color(0xFFD8D8D8),
                  backgroundImage: imageProvider,
                );
              },
              placeholder: (context, url) {
                return CircleAvatar(
                  radius: 156.r,
                  backgroundColor: const Color(0xFFD8D8D8),
                  backgroundImage: R.image.default_avatar(),
                );
              },
              errorWidget: (context, url, error) {
                return CircleAvatar(
                  radius: 156.r,
                  backgroundColor: const Color(0xFFD8D8D8),
                  backgroundImage: R.image.default_avatar(),
                );
              },
              fit: BoxFit.cover,
            ),
          ),
          Text(
            doctor!.name,
            style: TextStyle(
              color: Colors.white,
              fontSize: 64.sp,
              height: 90 / 64,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            doctor!.organization,
            style: TextStyle(
              color: Colors.white,
              fontSize: 56.sp,
              height: 66 / 56,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

class _SpeakingCard extends StatelessWidget {
  const _SpeakingCard({this.doctor});
  final DoctorInfo? doctor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      margin: EdgeInsets.only(
        left: 25.w,
        right: 25.w,
        bottom: 15.h,
      ),
      padding: EdgeInsets.symmetric(
        vertical: 30.h,
        horizontal: 20.r,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.r),
        border: Border.all(color: const Color(0xFF979797)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            S.current.f_Mzuz0ys7,
            style: TextStyle(
              color: Colors.white,
              fontSize: 64.sp,
              height: 90 / 64,
              fontWeight: FontWeight.w500,
            ),
          ),
          Stack(
            children: [
              Container(
                padding: EdgeInsets.all(20.r),
                decoration: BoxDecoration(
                  color: const Color(0xFF2CC796),
                  borderRadius: BorderRadius.circular(350.r),
                ),
                child: CachedNetworkImage(
                  imageUrl: doctor!.avatarUrl,
                  imageBuilder: (context, imageProvider) {
                    return CircleAvatar(
                      radius: 156.r,
                      backgroundColor: const Color(0xFFD8D8D8),
                      backgroundImage: imageProvider,
                    );
                  },
                  placeholder: (context, url) {
                    return CircleAvatar(
                      radius: 156.r,
                      backgroundColor: const Color(0xFFD8D8D8),
                      backgroundImage: R.image.default_avatar(),
                    );
                  },
                  errorWidget: (context, url, error) {
                    return CircleAvatar(
                      radius: 156.r,
                      backgroundColor: const Color(0xFFD8D8D8),
                      backgroundImage: R.image.default_avatar(),
                    );
                  },
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                right: 0,
                bottom: 20.h,
                child: Image(
                  image: R.image.doctor_speaking(),
                  width: 115.r,
                  height: 115.r,
                ),
              )
            ],
          ),
          Text(
            doctor!.name,
            style: TextStyle(
              color: Colors.white,
              fontSize: 64.sp,
              height: 90 / 64,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            doctor!.organization,
            style: TextStyle(
              color: Colors.white,
              fontSize: 56.sp,
              height: 66 / 56,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
