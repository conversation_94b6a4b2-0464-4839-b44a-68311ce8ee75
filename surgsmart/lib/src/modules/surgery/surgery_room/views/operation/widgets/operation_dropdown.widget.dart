import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/models/device_setting.model.dart';
import 'package:surgsmart/src/tools/throttle_control.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/select.widget.dart';
import 'control_strip.widget.dart';

class ControlStripData {
  final String title;
  final double value;
  final ImageProvider activeImage;
  final ImageProvider? inactiveImage;

  ControlStripData({
    this.value = 0,
    this.title = '',
    required this.activeImage,
    this.inactiveImage,
  });
}

class OperationDropdown extends StatefulWidget {
  final String title;
  final String? noneTips;
  final List<DeviceSettingInfo>? dataList;
  final void Function(DeviceSettingInfo item)? onSelect;
  final void Function()? interceptOpenSelect;
  final void Function(bool value)? switchChange;

  final bool? switchState;
  final bool switchEnable;
  final double switchScale; //default 1.8
  final bool titleRowClickable;
  final bool selfControlState;
  final bool showTips;

  final ControlStripData? controlStripData;
  final Widget? footer;
  final Widget? moreSettings;
  final BoxDecoration? decoration;

  const OperationDropdown(
      {super.key,
      required this.title,
      this.moreSettings,
      this.dataList,
      this.onSelect,
      this.interceptOpenSelect,
      this.noneTips,
      this.controlStripData,
      this.switchState,
      this.switchChange,
      this.switchEnable = true,
      this.switchScale = 1.8,
      this.titleRowClickable = true,
      this.selfControlState = true,
      this.showTips = false,
      this.decoration,
      this.footer});

  @override
  State<OperationDropdown> createState() => _OperationDropdownState();
}

class _OperationDropdownState extends State<OperationDropdown> {
  int selectIndex = 0;
  bool? switchState;
  bool switchEnable = true;

  @override
  void initState() {
    super.initState();
    switchState = widget.switchState;
    switchEnable = widget.switchEnable;
  }

  @override
  void didUpdateWidget(covariant OperationDropdown oldWidget) {
    if (oldWidget.switchState != widget.switchState ||
        oldWidget.switchEnable != widget.switchEnable) {
      setState(() {
        switchState = widget.switchState;
        switchEnable = widget.switchEnable;
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    switchState ??= widget.switchState;
    List<SelectItem> items = createItems();
    return Container(
        width: double.infinity,
        padding: widget.decoration == null ? EdgeInsets.all(30.r) : null,
        decoration: widget.decoration ??
            BoxDecoration(
              color: const Color(0xFF1D2632),
              borderRadius: BorderRadius.circular(24.r),
              border: Border.all(
                color: const Color(0xFF313E50),
                width: 8.r,
              ),
            ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                if (widget.titleRowClickable) {
                  switchHandler();
                }
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.title,
                    style: TextStyle(
                      color: switchEnable
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                      fontSize: 56.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (widget.moreSettings != null) widget.moreSettings!,
                  if (widget.switchState != null)
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        switchHandler();
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: 40.w, right: 20.w, top: 10.h, bottom: 10.h),
                        child: Transform.scale(
                          scale: widget.switchScale.w,
                          child: Switch(
                            value: switchState!,
                            onChanged: null,
                            splashRadius: 0,
                            trackColor: WidgetStateProperty.resolveWith<Color>(
                              (states) {
                                if (states.contains(WidgetState.selected)) {
                                  return const Color(0xFF22D1B6);
                                }
                                return Colors.transparent;
                              },
                            ),
                            activeColor: Colors.white,
                            inactiveThumbColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            if (widget.dataList != null) ...[
              SizedBox(height: 20.h),
              Select(
                id: widget.title,
                height: 100.h,
                placeholder: (widget.dataList!.isEmpty || widget.showTips)
                    ? widget.noneTips
                    : widget.dataList![selectIndex].device,
                backgroundColor: const Color(0xff1D2632),
                borderSide:
                    BorderSide(width: 4.w, color: const Color(0xff313E50)),
                disabled: widget.dataList!.isEmpty,
                items: items,
                maxShowCount: 4,
                interceptOpenSelect: widget.interceptOpenSelect,
                onSelect: (index) {
                  selectIndex = index;
                  //同步原始数据状态
                  for (int i = 0; i < widget.dataList!.length; i++) {
                    widget.dataList![i].selected = i == index;
                  }
                  widget.onSelect?.call(widget.dataList![index]);
                  setState(() {});
                },
              ),
            ],
            if (widget.controlStripData != null &&
                widget.dataList?.isNotEmpty == true) ...[
              SizedBox(
                  height:
                      widget.controlStripData!.title.isNotEmpty ? 24.h : 50.h),
              ControlStrip(
                title: widget.controlStripData!.title,
                activeImage: widget.controlStripData!.activeImage,
                inactiveImage: widget.controlStripData?.inactiveImage,
                value: widget.dataList![selectIndex].value.toDouble(),
                slidingEnd: (value) {
                  widget.dataList![selectIndex].value = value.toInt();
                  widget.onSelect?.call(widget.dataList![selectIndex]);
                },
                slidingChange: (value) {
                  widget.dataList![selectIndex].value = value.toInt();
                  widget.onSelect?.call(widget.dataList![selectIndex]);
                },
              ),
              SizedBox(height: 10.h),
            ],
            if (widget.footer != null) ...[
              SizedBox(height: 30.h),
              widget.footer!,
            ],
          ],
        ));
  }

  List<SelectItem> createItems() {
    List<SelectItem> selectItems = [];
    if (widget.dataList != null) {
      for (int i = 0; i < widget.dataList!.length; i++) {
        selectItems.add(
          SelectItem(
            text: widget.dataList![i].device,
            selected: widget.dataList![i].selected,
          ),
        );
        if (widget.dataList![i].selected) {
          selectIndex = i;
        }
      }
    }
    return selectItems;
  }

  void switchHandler() {
    if (switchState != null && switchEnable) {
      if (widget.dataList != null && widget.dataList!.isEmpty) {
        ToastUtils.showToast(message: widget.noneTips);
        return;
      }
      if (Throttle().checkPass("switchState")) {
        if(widget.selfControlState){
          setState(() {
            switchState = !switchState!;
          });
          widget.switchChange?.call(switchState!);
        }else{
          widget.switchChange?.call(!switchState!);
        }
      }
    }
  }
}
