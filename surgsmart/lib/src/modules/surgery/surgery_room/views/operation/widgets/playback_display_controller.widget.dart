import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/http/room.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';

class PlaybackDisplayControllerWidget extends StatelessWidget {
  final SurgeryRoomController controller;
  const PlaybackDisplayControllerWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return AppBackground(
      width: 941.w,
      radius: 30.r,
      padding: EdgeInsets.only(left: 40.w, right: 40.w),
      child: ValueListenableBuilder(
        valueListenable: controller.largeScreenEnable,
        builder: (context, largeScreenEnable, child) {
          return ValueListenableBuilder(
            valueListenable: controller.playbackLargeScreenSync,
            builder: (context, value, child) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  32.verticalSpace,
                  Row(
                    children: [
                      Image(
                        image: R.image.logo_variant(),
                        width: 330.w,
                        height: 77.h,
                      ),
                    ],
                  ),
                  64.verticalSpace,
                  // if (controller.playbackControl.value?.playbackType ==
                  //     PlaybackType.bleedPlay) ...[
                  //   OperationDropdown(
                  //     title: '大屏同步展示',
                  //     switchState: largeScreenEnable && value,
                  //     switchChange: (val) {
                  //       controller.playbackLargeScreenSync.value = val;
                  //     },
                  //     switchEnable: largeScreenEnable,
                  //   ),
                  //   largeScreenEnable
                  //       ? Padding(
                  //           padding: EdgeInsets.only(top: 84.h),
                  //           child: Image(
                  //             width: 450.w,
                  //             height: 282.h,
                  //             image: controller.playbackLargeScreenSync.value
                  //                 ? R.image.screen_displayed()
                  //                 : R.image.screen_displayable(),
                  //           ),
                  //         )
                  //       : Padding(
                  //           padding: EdgeInsets.only(top: 148.h),
                  //           child: Text('未连接到显示设备',
                  //               style: TextStyle(
                  //                 color: Colors.white,
                  //                 fontSize: 56.sp,
                  //               )),
                  //         ),
                  // ],
                  // if (controller.playbackControl.value?.playbackType ==
                  //     PlaybackType.surgPlay) ...[
                  Image(
                    width: 330.w,
                    height: 330.h,
                    image: R.image.playback(),
                  ),
                  44.verticalSpace,
                  Text(
                    controller.playbackControl.value?.playbackType ==
                            PlaybackType.bleedPlay
                        ? '出血回放'
                        : S.current.f_8PBHcoUd,
                    style: TextStyle(
                      fontSize: 96.sp,
                      color: Colors.white,
                    ),
                  ),
                  //],
                  Spacer(),
                  Button(
                    onPressed: () {
                      controller.toggleBloodPlayback();
                    },
                    label: Container(
                      width: 464.w,
                      padding: EdgeInsets.symmetric(
                          horizontal: 104.w, vertical: 16.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(55.r),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            const Color(0xB25CF5EB),
                            const Color(0xB256B3E3)
                          ],
                        ),
                      ),
                      child: Center(
                        child: Text(
                          S.current.f_pFbaCUzb,
                          style: TextStyle(
                            fontSize: 56.sp,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  64.verticalSpace,
                ],
              );
            },
          );
        },
      ),
    );
  }
}
