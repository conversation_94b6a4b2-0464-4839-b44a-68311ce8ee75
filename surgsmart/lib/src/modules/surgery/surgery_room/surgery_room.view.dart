import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:blur/blur.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:medias_kit/monitor/monitor_view.dart';
import 'package:screenshot/screenshot.dart' as screenshot;
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/http/room.model.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/algorithm/algorithm.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/algorithm/algorithm.view.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/operation.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/operation.view.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/playback_display_controller.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/drawing_board.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/play_back.widget.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/corner_container.widget.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/playback_control.widget.dart';
import 'package:surgsmart/src/widgets/smart_voice.widget.dart';
import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;

import '../../../widgets/screenshot_animation.widget.dart';
import 'surgery_room.controller.dart';

/// 所属模块: surgery
///
/// 手术
class SurgeryRoomView extends AppView<SurgeryRoomController> {
  final GlobalKey<SurgPlaybackState> playbackKey = GlobalKey();
  SurgeryRoomView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, OfflineSurgeryData surgeryData) {
    var operationPanel = Container(
      width: 941.w,
      height: double.infinity,
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(30.r)),
      child: OperationView(
        key: key,
        binding: (key) => OperationController(key, null),
      ),
    );

    var algorithmPanel = Container(
      width: 941.w,
      height: double.infinity,
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(30.r)),
      child: AlgorithmView(
        key: key,
        binding:
            (key) => AlgorithmController(
              key,
              null,
              surgeryId: surgeryData.localSurgeryId,
              procedure: surgeryData.readProcedure()!,
              enableAdvanceAi: controller.enableAdvanceAi,
            ),
      ),
    );

    return Padding(
      padding: EdgeInsets.all(24.r),
      child: ValueListenableBuilder(
        valueListenable: controller.playbackControl,
        builder: (context, playbackInfo, child) {
          var monitorPanel = Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w),
            height: double.infinity,
            child: Stack(
              children: [
                if (Platform.isLinux) ...[
                  Container(
                    height: double.infinity,
                    clipBehavior: Clip.hardEdge,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: screenshot.Screenshot(
                      controller: controller.videoScreenshotController,
                      child: Stack(
                        children: [
                          MonitorView(monitor: controller.monitorController),
                          ValueListenableBuilder(
                            valueListenable: controller.isBodyout,
                            builder: (context, isBodyout, child) {
                              return ValueListenableBuilder(
                                valueListenable: controller.outsideBlurIsOn,
                                builder: (context, outsideBlurIsOn, child) {
                                  return Visibility(
                                    visible: outsideBlurIsOn && isBodyout,
                                    child: Positioned.fill(
                                      child: const SizedBox.shrink().frosted(
                                        frostColor: Colors.black,
                                        blur: 50,
                                        borderRadius: BorderRadius.circular(
                                          20.r,
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                          vertical: double.infinity,
                                          horizontal: double.infinity,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                          ValueListenableBuilder(
                            valueListenable: controller.graphics,
                            builder: (context, graphics, child) {
                              /// 专家绘线
                              return Stack(
                                children: [
                                  ...controller.graphics.value.map(
                                    (graphic) => Positioned.fill(
                                      child: DrawingBoard(
                                        drawingBoardType: DrawingBoardType.line,
                                        userGraphic: graphic,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  //手指
                  ValueListenableBuilder(
                    valueListenable: controller.graphics,
                    builder: (context, graphics, child) {
                      return playbackInfo != null
                          ? SizedBox.shrink()
                          : Stack(
                            children: [
                              ...controller.graphics.value.map(
                                (graphic) => Positioned.fill(
                                  child: DrawingBoard(
                                    drawingBoardType: DrawingBoardType.finger,
                                    userGraphic: graphic,
                                  ),
                                ),
                              ),
                            ],
                          );
                    },
                  ),
                  AnimatedBuilder(
                    animation: Listenable.merge([
                      controller.isShowExternalVideo,
                      controller.canShowExternalVideo,
                    ]),
                    builder: (context, child) {
                      return Visibility(
                        visible:
                            controller.isShowExternalVideo.value &&
                            controller.canShowExternalVideo.value,
                        child: Positioned(
                          width: 576.w,
                          height: 324.h,
                          bottom: 0,
                          right: 0,
                          child:
                              controller.secondaryWithaiRTC?.videoRender == null
                                  ? const SizedBox.shrink()
                                  : msp.RTCVideoView(
                                    controller.secondaryWithaiRTC!.videoRender!,
                                  ),
                          // controller.monitor2Controller == null
                          //     ? const SizedBox.shrink()
                          //     : MonitorView(
                          //       monitorController:
                          //           controller.monitor2Controller!,
                          //     ),
                        ),
                      );
                    },
                  ),
                  ValueListenableBuilder(
                    valueListenable: controller.isShowExternalVideo,
                    builder: (context, value, child) {
                      return Visibility(
                        visible: value,
                        child: Positioned(
                          width: 576.w,
                          height: 324.w,
                          bottom: 0,
                          right: 0,
                          child:
                              controller.secondaryWithaiRTC?.videoRender == null
                                  ? const SizedBox.shrink()
                                  : msp.RTCVideoView(
                                    controller.secondaryWithaiRTC!.videoRender!,
                                  ),
                          // controller.monitor2Controller == null
                          //     ? const SizedBox.shrink()
                          //     : MonitorView(
                          //       monitorController:
                          //           controller.monitor2Controller!,
                          //     ),
                        ),
                      );
                    },
                  ),
                ],
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 32.r,
                    vertical: 32.r,
                  ),
                  child: ValueListenableBuilder(
                    valueListenable: AppContext.share.networkConnected,
                    builder: (context, networkConnected, child) {
                      return Row(
                        children: [
                          Text(
                            surgeryData.readProcedure()?.name ?? "未知术式",
                            style: TextStyle(
                              fontSize: 52.sp,
                              height: 67 / 52,
                              fontWeight: FontWeight.w500,
                            ),
                          ).frosted(
                            frostColor: const Color(0xFF777777),
                            blur: 10,
                            borderRadius: BorderRadius.circular(10.r),
                            padding: EdgeInsets.symmetric(
                              vertical: 30.h,
                              horizontal: 42.w,
                            ),
                          ),
                          const Spacer(),
                          // 手术协同医生人数
                          Visibility(
                            visible: networkConnected,
                            child: ValueListenableBuilder(
                              valueListenable: controller.doctors,
                              builder: (
                                BuildContext context,
                                doctors,
                                Widget? child,
                              ) {
                                return Visibility(
                                  visible: doctors.isNotEmpty,
                                  child: Padding(
                                    padding: EdgeInsets.only(right: 18.w),
                                    child: CornerContainer(
                                      showIndicator: false,
                                      backgroundColor: Colors.transparent,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Image(
                                            image: R.image.rtc_live(),
                                            width: 50.r,
                                            height: 50.r,
                                          ),
                                          Text(
                                            " ${doctors.length}",
                                            style: TextStyle(
                                              fontSize: 48.sp,
                                              fontWeight: FontWeight.w500,
                                              height: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          // 直播在线人数
                          Visibility(
                            visible: networkConnected,
                            child: ValueListenableBuilder(
                              valueListenable: controller.liveUserCount,
                              builder: (context, value, child) {
                                return Visibility(
                                  visible: value != null,
                                  child: Padding(
                                    padding: EdgeInsets.only(right: 18.w),
                                    child: CornerContainer(
                                      showIndicator: false,
                                      backgroundColor: Colors.transparent,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Image(
                                            image: R.image.rtm_live(),
                                            width: 50.r,
                                            height: 50.r,
                                          ),
                                          Text(
                                            " $value",
                                            style: TextStyle(
                                              fontSize: 48.sp,
                                              fontWeight: FontWeight.w500,
                                              height: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          // RTC
                          if (!controller.noSignalAll())
                            Padding(
                              padding: EdgeInsets.only(right: 18.w),
                              child: CornerContainer(
                                backgroundColor: Colors.transparent,
                                showIndicator: false,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                          width: 23.r,
                                          height: 23.r,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFD4353F),
                                            borderRadius: BorderRadius.circular(
                                              12.5.r,
                                            ),
                                          ),
                                        )
                                        .animate(
                                          onComplete: (controller) {
                                            controller.repeat(reverse: true);
                                          },
                                        )
                                        .fade(duration: 500.ms),
                                    SizedBox(
                                      height: 50.h,
                                      child: Text(
                                        " REC",
                                        style: TextStyle(
                                          fontSize: 45.sp,
                                          fontWeight: FontWeight.w500,
                                          height: 1,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          // 网络
                          CornerContainer(
                            backgroundColor: Colors.transparent,
                            showIndicator: false,
                            child: Row(
                              children: [
                                ValueListenableBuilder(
                                  valueListenable: controller.networkTypeChange,
                                  builder: (context, value, child) {
                                    return SvgPicture.asset(
                                      AppContext.share
                                          .getNetWorkStatusIcon()
                                          .keyName,
                                      width: 50.r,
                                      height: 50.r,
                                    );
                                  },
                                ),
                                Visibility(
                                  visible: networkConnected,
                                  child: ValueListenableBuilder(
                                    valueListenable: controller.videoBitrate,
                                    builder: (context, value, child) {
                                      final speed =
                                          controller.doctors.value.isEmpty
                                              ? 0
                                              : value;
                                      return Text.rich(
                                        TextSpan(
                                          text: " $speed",
                                          style: TextStyle(
                                            fontSize: 35.sp,
                                            fontWeight: FontWeight.w500,
                                            height: 1,
                                          ),
                                          children: [
                                            TextSpan(
                                              text: "kb/s",
                                              style: TextStyle(
                                                fontSize: 25.sp,
                                                fontWeight: FontWeight.w500,
                                                height: 1,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: controller.isShowSignalMessage,
                  builder: (context, isShowSignalMessage, child) {
                    return Visibility(
                      visible: isShowSignalMessage,
                      child: Positioned.fill(
                        child: Center(
                          child: Text(
                            S.current.f_zRtPo40Z,
                            style: TextStyle(
                              fontSize: 80.r,
                              fontWeight: FontWeight.w500,
                            ),
                          ).frosted(
                            frostColor: Colors.black,
                            blur: 30,
                            borderRadius: BorderRadius.circular(20.r),
                            padding: EdgeInsets.symmetric(
                              vertical: 45.h,
                              horizontal: 147.w,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
                ValueListenableBuilder(
                  valueListenable: controller.markImagePath,
                  builder: (context, imagePath, child) {
                    return ScreenshotAnimateWidget(imagePath: imagePath);
                  },
                ),
                if (playbackInfo?.playbackType == PlaybackType.bleedPlay &&
                    controller.largeScreenEnable.value)
                  FutureBuilder(
                    future: Future.delayed(Duration(seconds: 10)),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.done) {
                        return const SizedBox.shrink();
                      }
                      return Positioned(
                        left: 42.w,
                        bottom: 37.h,
                        width: 1800.w,
                        height: 210.h,
                        child: SmartVoice(
                          state: DisplayState.activate,
                          maxContentLength: 100,
                          content: '疑似严重出血，M3将自动进行回放',
                          showBottomDynamicEffect: false,
                        ),
                      );
                    },
                  ),
              ],
            ),
          );

          return Stack(
            children: [
              Positioned.fill(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    playbackInfo != null
                        ? PlaybackDisplayControllerWidget(
                          controller: controller,
                        )
                        : operationPanel,
                    Expanded(
                      child: Stack(
                        children: [
                          monitorPanel,
                          // 无信号弹框
                          ValueListenableBuilder(
                            valueListenable:
                                controller.recordController.hasSignal,
                            builder: (context, hasSignal, child) {
                              if (hasSignal) {
                                controller.isShowNoSignalDialog = false;
                                controller.sendSignalMessageToWindow();

                                return SizedBox.shrink();
                              }
                              return noSignalDialog();
                            },
                          ),

                          if (playbackInfo != null)
                            screenshot.Screenshot(
                              controller:
                                  controller.playbackScreenshotController,
                              child: SurgPlayback(
                                key: playbackKey,
                                playbackInfo: playbackInfo,
                                rate:
                                    playbackInfo.playbackType ==
                                            PlaybackType.bleedPlay
                                        ? 0.5
                                        : 1.0,
                                showProgressBar:
                                    playbackInfo.playbackType ==
                                    PlaybackType.surgPlay,
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (playbackInfo != null)
                      VideoSetupPanel(playbackKey: playbackKey),
                    Offstage(
                      offstage: playbackInfo != null,
                      child: algorithmPanel,
                    ),
                  ],
                ),
              ),
              ValueListenableBuilder(
                valueListenable: controller.showSurgPlayback,
                builder: (context, show, child) {
                  return show
                      ? Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                          ),
                          child: loadingWidget(S.current.f_Lknk4xXX),
                        ),
                      )
                      : Container();
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    if (controller.loadingStatus != null) {
      return loadingWidget(controller.loadingStatus!);
    }
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  Widget loadingWidget(String text) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        CupertinoActivityIndicator(
          radius: 160.r,
          color: const Color(0xFF297BFF),
        ),
        SizedBox(height: 40.h),
        Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 64.sp),
        ),
      ],
    );
  }

  Widget noSignalDialog() {
    return FutureBuilder(
      future: Future.delayed(
        controller.isShowNoSignalDialog ? Duration.zero : 3.seconds,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done &&
            !controller.recordController.hasSignal.value) {
          controller.isShowNoSignalDialog = true;
          controller.sendSignalMessageToWindow();
          return Positioned.fill(
            child: Container(
              color: Colors.black.withValues(alpha: 0.6),
              width: double.infinity,
              height: double.infinity,
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: 1100.w,
                    maxHeight: 758.h,
                  ),
                  child: Modal(
                    header: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFD4353F),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(24.w),
                          topRight: Radius.circular(24.w),
                        ),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 48.w,
                        vertical: 24.h,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color: const Color(0xffffffff),
                            size: 80.r,
                          ),
                          SizedBox(width: 24.w),
                          Text(
                            S.current.f_9tIRXX9I,
                            style: TextStyle(
                              color: const Color(0xFFffffff),
                              fontSize: 56.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    kind: ModalKind.dialog,
                    type: ModalType.info,
                    messageFontSize: 72.sp,
                    confirmText: S.current.f_9vOSkWmP,
                    message: S.current.f_9vOSE1Gk,
                    onConfirm: () async {
                      app.openInner(GoPaths.linkTutorial);
                    },
                  ),
                ),
              ),
            ),
          );
        }
        controller.isShowNoSignalDialog = false;
        controller.sendSignalMessageToWindow();

        return SizedBox.shrink();
      },
    );
  }
}
