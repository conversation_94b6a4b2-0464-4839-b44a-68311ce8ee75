import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;
import 'package:surgsmart/src/rtc/rtc_client.dart';
import 'package:surgsmart/src/rtc/rtc_consumer.dart';
import 'package:surgsmart/src/rtc/rtc_producer.dart';
import 'package:surgsmart/src/rtc/rtc_signal.dart';
import 'package:surgsmart/src/rtc/rtc_signal.model.dart';
import 'package:surgsmart/src/rtc/rtc_socketio.signal.dart';

class WithaiRTC implements RtcClientObserver, RtcSocketioSignalObserver {
  late final bool isMainStream;

  late final RtcClient _rtcClient;

  final signalStatus = RtcSignalStatus.disconnected.notifier;

  msp.RTCVideoRenderer? _videoRender;

  msp.RTCVideoRenderer? get videoRender => _videoRender;

  msp.RTCVideoRenderer? _audioRender;

  msp.MediaStream? _remoteAudioStream;

  msp.MediaStream? _videoStream;

  msp.MediaStream? _audioStream;

  RtcProducer? _videoProducer;

  RtcProducer? _audioProducer;

  final _audioConsumers = <String, RtcConsumer>{};

  Timer? _timer;

  List<msp.StatsReport>? lastState;

  void Function(int userId)? onUserJoined;

  void Function(int userId)? onUserLeft;

  WithaiRTC._();

  factory WithaiRTC.init({
    required String url,
    required String authToken,
    required bool isMainStream,
    void Function(WithaiRTC rtc)? onSignalStatus,
    void Function(int userId)? onUserJoined,
    void Function(int userId)? onUserLeft,
    void Function(int bitrate)? onTransportStats,
  }) {
    final rtc = WithaiRTC._();
    rtc.isMainStream = isMainStream;
    rtc.onUserJoined = onUserJoined;
    rtc.onUserLeft = onUserLeft;

    rtc._rtcClient = RtcClient(
      RtcSocketioSignal(url, authToken, WeakReference(rtc)),
      WeakReference(rtc),
    );
    rtc.signalStatus.addListener(() => onSignalStatus?.call(rtc));

    rtc._timer = Timer.periodic(Duration(seconds: 1), (timer) async {
      if (rtc._videoProducer == null) {
        rtc.lastState = null;
        onTransportStats?.call(0);
        return;
      }
      final stats = await rtc._rtcClient.getState();
      final info =
          stats
              .firstWhere(
                (element) =>
                    element.type == 'outbound-rtp' &&
                    element.values["kind"] == "video" &&
                    element.values["mediaSourceId"] != null,
              )
              .values;
      if (rtc.lastState != null) {
        final lastInfo =
            rtc.lastState!
                .firstWhere(
                  (element) =>
                      element.type == 'outbound-rtp' &&
                      element.values["kind"] == "video" &&
                      element.values["mediaSourceId"] != null,
                )
                .values;
        final bitrate =
            ((info["bytesSent"] - lastInfo["bytesSent"]) * 8 / 1000).toInt();
        onTransportStats?.call(bitrate);
      }
      rtc.lastState = stats;
    });
    return rtc;
  }

  Future<void> dispose() async {
    _timer?.cancel();
    _timer = null;
    _videoStream = null;
    _audioStream = null;
    await _remoteAudioStream?.dispose();
    _remoteAudioStream = null;
    await _videoRender?.dispose();
    _videoRender = null;
    await _audioRender?.dispose();
    _audioRender = null;
    _audioConsumers.forEach((key, consumer) async => await consumer.close());
    _audioConsumers.clear();
    await _videoProducer?.close();
    _videoProducer = null;
    await _audioProducer?.close();
    _audioProducer = null;
    await _rtcClient.dispose();
    signalStatus.dispose();
  }

  Future<void> joinRoom() async {
    try {
      await _rtcClient.joinRoom();
      app.logI("加入房间成功");
    } catch (error) {
      app.logE(error);
      rethrow;
    }
  }

  Future<void> leaveRoom() async {
    try {
      await _rtcClient.leaveRoom();
      app.logI("离开房间成功");
    } catch (error) {
      app.logE(error);
      rethrow;
    }
  }

  Future<void> publishVideo(String videoInputName) async {
    if (_videoProducer != null) {
      return;
    }
    try {
      final devices = await msp.mediaDevices.enumerateDevices();
      final device = devices.firstWhere((d) => d.label == videoInputName);
      _videoStream = await msp.mediaDevices.getUserMedia({
        'video': {
          'deviceId': device.deviceId,
          'optional': [
            {'sourceId': device.deviceId},
          ],
          'width': {'min': 1280},
          'height': {'min': 720},
          'framerate': {'min': 25},
        },
      });
      if (!isMainStream && _videoRender == null) {
        _videoRender = msp.RTCVideoRenderer();
        await _videoRender!.initialize();
        _videoRender?.srcObject = _videoStream;
      }
      _videoProducer = await _rtcClient.newProducer(
        rtcCodec: RtcCodec.h264,
        identifier: "${isMainStream ? "main" : "secondary"}-video",
        track: _videoStream!.getVideoTracks().firstWhere(
          (track) => track.kind == 'video',
        ),
        stream: _videoStream!,
      );
    } catch (e) {
      app.logE(e);
      rethrow;
    }
  }

  Future<void> publishAudio() async {
    if (_audioProducer != null) {
      return;
    }
    try {
      _audioStream = await msp.mediaDevices.getUserMedia({
        'audio': {
          'sampleRate': 48000, // 采样率设置为48kHz
          'channelCount': 1, // 声道数（1=单声道，2=立体声）
          'echoCancellation': true, // 回声消除
          'noiseSuppression': true, // 噪声抑制
          'autoGainControl': true, // 自动增益控制
        },
      });
      _audioProducer = await _rtcClient.newProducer(
        identifier: "${isMainStream ? "main" : "secondary"}-audio",
        track: _audioStream!.getAudioTracks().firstWhere(
          (track) => track.kind == 'audio',
        ),
        stream: _audioStream!,
      );
    } catch (e) {
      app.logE(e);
      rethrow;
    }
  }

  Future<void> unpublishVideo() async {
    try {
      _videoStream = null;
      await _videoProducer?.close();
      _videoProducer = null;
    } catch (e) {
      app.logE(e);
      rethrow;
    }
  }

  Future<void> unpublishAudio() async {
    try {
      _audioStream = null;
      await _audioProducer?.close();
      _audioProducer = null;
    } catch (e) {
      app.logE(e);
      rethrow;
    }
  }

  @override
  void rtcDebug(String source, message) {
    app.logD("$source: $message");
  }

  @override
  void rtcError(String source, message) {
    app.logE("$source: $message");
  }

  @override
  void rtcSignalException(message) {
    app.logE(message);
  }

  @override
  void rtcSignalNewConsumer(RtcStreamOption streamOption) async {
    if (!isMainStream) {
      return;
    }
    if (streamOption.producerPeerId == _rtcClient.peerSelf.id) {
      return;
    }
    if (streamOption.producerPeerId.substring(
          0,
          streamOption.producerPeerId.length - 4,
        ) ==
        _rtcClient.peerSelf.id) {
      return;
    }

    if (streamOption.kind == msp.RTCRtpMediaType.RTCRtpMediaTypeAudio) {
      try {
        _remoteAudioStream ??= await msp.createLocalMediaStream("remote-audio");
        if (_audioRender == null) {
          _audioRender = msp.RTCVideoRenderer();
          await _audioRender!.initialize();
          _audioRender!.srcObject = _remoteAudioStream;
        }
        _rtcClient.newConsumer(streamOption).then((consumer) async {
          _audioConsumers[streamOption.producerPeerId] = consumer;
          _remoteAudioStream?.addTrack(consumer.track);
          await consumer.resume();
        });
      } catch (e) {
        app.logE(e);
        rethrow;
      }
    }
  }

  @override
  void rtcSignalPeerJoined(RtcPeer peer) {
    onUserJoined?.call(int.parse(peer.id));
  }

  @override
  void rtcSignalPeerLeft(RtcPeer peer) {
    onUserLeft?.call(int.parse(peer.id));
    final consumer = _audioConsumers.remove(peer.id);
    if (consumer != null) {
      _remoteAudioStream?.removeTrack(consumer.track);
      consumer.close();
    }
  }

  @override
  void rtcSignalPeerUpdated(RtcPeer peer) {}

  @override
  void rtcSignalStatusChanged(RtcSignalStatus status) {
    signalStatus.value = status;
  }

  RtcProducer? getAudioProducer() {
    return _audioProducer;
  }

  RtcProducer? getVideoProducer() {
    return _videoProducer;
  }
}
