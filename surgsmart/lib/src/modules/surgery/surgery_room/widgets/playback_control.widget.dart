import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/play_back.widget.dart';
import 'package:surgsmart/src/tools/throttle_control.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';

class VideoSetupPanel extends StatefulWidget {
  final Function(bool frameDisplay)? frameDisplay;
  final GlobalKey<SurgPlaybackState> playbackKey;

  const VideoSetupPanel({
    super.key,
    required this.playbackKey,
    this.frameDisplay,
  });

  @override
  State<VideoSetupPanel> createState() {
    return VideoSetupPanelState();
  }
}

class VideoSetupPanelState extends State<VideoSetupPanel> {
  bool frameDisplayOn = false;
  bool seekPreviousFrame = true;
  bool seekNextFrame = true;

  final String specificSpeed = '逐帧播放';
  String currentSpeed = "";

  List<double> speedList = const [
    0.1,
    0.2,
    0.3,
    0.4,
    0.5,
    1.0,
    1.5,
    2.0,
    2.5,
    3.0,
  ];

  List<double> zoomList = const [1.0, 1.5, 2.0, 2.5, 3.0];
  double currentZoom = 1.0;
  bool seekPreviousFrameTapDown = false;
  bool seekNextFrameTapDown = false;
  Timer? _frameSeekTime;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 100)).then((val) {
      setState(() {
        currentSpeed =
            widget.playbackKey.currentState != null
                ? widget.playbackKey.currentState!.rate.toString()
                : "1.0";
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBackground(
      width: 941.w,
      padding: EdgeInsets.only(top: 96.h, left: 48.w, right: 48.w),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                S.current.f_9t13TQQo,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 64.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              60.horizontalSpace,
              Padding(
                padding: EdgeInsets.only(top: 20.w),
                child: Transform.scale(
                  scale: 2.2.w,
                  child: Switch(
                    value: frameDisplayOn,
                    onChanged: (val) {
                      setState(() {
                        frameDisplayOn = val;
                        widget.frameDisplay?.call(val);
                      });
                      widget.playbackKey.currentState?.frameMode(val);
                    },
                    splashRadius: 0,
                    trackColor: WidgetStateProperty.resolveWith<Color>((
                      states,
                    ) {
                      if (states.contains(WidgetState.selected)) {
                        return const Color(0xFF22D1B6);
                      }
                      return Colors.transparent;
                    }),
                    activeColor: Colors.white,
                    inactiveThumbColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          60.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: GestureDetector(
                  onTap:
                      frameDisplayOn
                          ? () {
                            seekPreviousFrame =
                                widget.playbackKey.currentState
                                    ?.previousFrame() ??
                                false;
                            if (seekPreviousFrame) {
                              seekNextFrame = true;
                            } else {
                              ToastUtils.showToast(message: '已经是第一帧了');
                            }
                            setState(() {});
                          }
                          : null,
                  onLongPress: () {
                    if (!frameDisplayOn) return;
                    setState(() {
                      seekPreviousFrameTapDown = true;
                      seekNextFrameTapDown = false;
                      seekNextFrame = true;
                    });
                    frameSeekTime();
                  },
                  onLongPressUp: () {
                    if (!frameDisplayOn) return;

                    setState(() {
                      seekPreviousFrameTapDown = false;
                    });
                  },
                  child: Container(
                    width: 400.w,
                    height: 150.h,
                    decoration: BoxDecoration(
                      color:
                          seekPreviousFrameTapDown
                              ? const Color(0x500EC6D2)
                              : Color(
                                frameDisplayOn && seekPreviousFrame
                                    ? 0x33FFFFFF
                                    : 0x1AFFFFFF,
                              ),
                      borderRadius: BorderRadius.circular(84.r),
                    ),
                    child: Center(
                      child: Text(
                        S.current.f_9t13jdZI,
                        style: TextStyle(
                          fontSize: 80.sp,
                          color:
                              frameDisplayOn && seekPreviousFrame
                                  ? Colors.white
                                  : Color(0x99FFFFFF),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 40),
              Flexible(
                child: GestureDetector(
                  onTap:
                      frameDisplayOn
                          ? () {
                            seekNextFrame =
                                widget.playbackKey.currentState?.nextFrame() ??
                                false;
                            if (!seekNextFrame) {
                              ToastUtils.showToast(message: '已经是最后一帧了');
                            } else {
                              seekPreviousFrame = true;
                            }
                            setState(() {});
                          }
                          : null,
                  onLongPress: () {
                    if (!frameDisplayOn) return;

                    setState(() {
                      seekNextFrameTapDown = true;
                      seekPreviousFrameTapDown = false;
                      seekPreviousFrame = true;
                    });
                    frameSeekTime();
                  },
                  onLongPressUp: () {
                    if (!frameDisplayOn) return;

                    setState(() {
                      seekNextFrameTapDown = false;
                    });
                  },
                  child: Container(
                    width: 400.w,
                    height: 150.h,
                    decoration: BoxDecoration(
                      color:
                          seekNextFrameTapDown
                              ? const Color(0x500EC6D2)
                              : Color(
                                frameDisplayOn && seekNextFrame
                                    ? 0x33FFFFFF
                                    : 0x1AFFFFFF,
                              ),
                      borderRadius: BorderRadius.circular(84.r),
                    ),
                    child: Center(
                      child: Text(
                        S.current.f_9t13Moc6,
                        style: TextStyle(
                          fontSize: 80.sp,
                          color:
                              frameDisplayOn && seekNextFrame
                                  ? Colors.white
                                  : Color(0x99FFFFFF),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          94.verticalSpace,
          Row(
            children: [
              SizedBox(
                width: 260.w,
                child: Text(
                  S.current.f_9t13dlGb,
                  style: TextStyle(fontSize: 64.sp, color: Colors.white),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                child: Padding(
                  padding: EdgeInsets.only(left: 50.w, top: 50.h, bottom: 50.h),
                  child: controlButton(
                    clickable: !frameDisplayOn && currentSpeed != specificSpeed,
                    image: R.image.minus(),
                  ),
                ),
                onTap: () {
                  if (frameDisplayOn ||
                      currentSpeed.isEmpty ||
                      currentSpeed == specificSpeed) {
                    return;
                  }
                  if (!Throttle().checkPass(
                    'playSpeedControl',
                    intervalMs: 500,
                  )) {
                    return;
                  }

                  if (currentSpeed == speedList.first.toString()) {
                    setState(() {
                      currentSpeed = specificSpeed;
                    });
                    widget.playbackKey.currentState?.playFrame();

                    return;
                  }
                  setState(() {
                    currentSpeed =
                        speedList[speedList.indexOf(
                                  double.tryParse(currentSpeed) ?? 1.0,
                                ) -
                                1]
                            .toString();
                  });
                  widget.playbackKey.currentState?.setRate(
                    double.parse(currentSpeed),
                  );
                },
              ),
              Expanded(
                child: Center(
                  child: Text(
                    currentSpeed == specificSpeed
                        ? currentSpeed
                        : currentSpeed.isNotEmpty
                        ? "x$currentSpeed"
                        : "",
                    style: TextStyle(
                      fontSize: currentSpeed == specificSpeed ? 64.sp : 120.sp,
                      color: Color(0xFF00EFFF),
                    ),
                  ),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                child: Padding(
                  padding: EdgeInsets.only(
                    right: 50.w,
                    top: 50.h,
                    bottom: 50.h,
                  ),
                  child: controlButton(
                    clickable:
                        !frameDisplayOn &&
                        currentSpeed != speedList.last.toString(),
                    image: R.image.add(),
                  ),
                ),
                onTap: () {
                  if (frameDisplayOn ||
                      currentSpeed.isEmpty ||
                      currentSpeed == speedList.last.toString()) {
                    return;
                  }
                  if (!Throttle().checkPass(
                    'playSpeedControl',
                    intervalMs: 500,
                  )) {
                    return;
                  }

                  setState(() {
                    if (currentSpeed == specificSpeed) {
                      currentSpeed = speedList.first.toString();
                    } else {
                      currentSpeed =
                          speedList[speedList.indexOf(
                                    double.tryParse(currentSpeed) ?? 1.0,
                                  ) +
                                  1]
                              .toString();
                    }
                    widget.playbackKey.currentState?.setRate(
                      double.parse(currentSpeed),
                    );
                  });
                },
              ),
            ],
          ),
          80.verticalSpace,
          Row(
            children: [
              SizedBox(
                width: 260.w,
                child: Text(
                  S.current.f_9t13avpE,
                  style: TextStyle(fontSize: 64.sp, color: Colors.white),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                child: Padding(
                  padding: EdgeInsets.only(left: 50.w, top: 50.h, bottom: 50.h),
                  child: controlButton(
                    clickable: currentZoom != zoomList.first,
                    image: R.image.minus(),
                  ),
                ),
                onTap: () {
                  if (currentZoom == zoomList.first) return;
                  setState(() {
                    currentZoom = zoomList[zoomList.indexOf(currentZoom) - 1];
                    widget.playbackKey.currentState?.setScale(currentZoom);
                  });
                },
              ),
              Expanded(
                child: Center(
                  child: Text(
                    "x$currentZoom",
                    style: TextStyle(
                      fontSize: 120.sp,
                      color: Color(0xFF00EFFF),
                    ),
                  ),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                child: Padding(
                  padding: EdgeInsets.only(
                    right: 50.w,
                    top: 50.h,
                    bottom: 50.h,
                  ),
                  child: controlButton(
                    clickable: currentZoom != zoomList.last,
                    image: R.image.add(),
                  ),
                ),
                onTap: () {
                  if (currentZoom == zoomList.last) return;
                  setState(() {
                    currentZoom = zoomList[zoomList.indexOf(currentZoom) + 1];
                    widget.playbackKey.currentState?.setScale(currentZoom);
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget controlButton({required bool clickable, required AssetImage image}) {
    return ColorFiltered(
      colorFilter: ColorFilter.mode(
        clickable ? Colors.white : Color(0x99FFFFFF),
        BlendMode.srcIn,
      ),
      child: Image(width: 90.w, height: 90.h, fit: BoxFit.fill, image: image),
    );
  }

  void frameSeekTime() {
    _frameSeekTime?.cancel();
    _frameSeekTime = Timer.periodic(Duration(milliseconds: 100), (timer) {
      if (!seekPreviousFrameTapDown && !seekNextFrameTapDown) {
        timer.cancel();
        return;
      }
      if (seekPreviousFrameTapDown) {
        seekPreviousFrame =
            widget.playbackKey.currentState?.previousFrame() ?? false;
        if (!seekPreviousFrame) {
          ToastUtils.showToast(message: '已经是第一帧了');
          timer.cancel();
        }
      } else if (seekNextFrameTapDown) {
        seekNextFrame = widget.playbackKey.currentState?.nextFrame() ?? false;
        if (!seekNextFrame) {
          ToastUtils.showToast(message: '已经是最后一帧了');
          timer.cancel();
        }
      }
    });
  }
}
