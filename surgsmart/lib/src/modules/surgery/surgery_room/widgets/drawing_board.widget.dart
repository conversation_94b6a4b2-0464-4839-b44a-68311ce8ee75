import 'dart:ui' as ui;
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';

enum DrawingBoardType {
  finger,
  line,
  fingerLine,
}

class DrawingBoard extends StatefulWidget {
  final UserGraphic userGraphic;
  final DrawingBoardType drawingBoardType;

  const DrawingBoard({
    super.key,
    required this.userGraphic,
    this.drawingBoardType = DrawingBoardType.fingerLine,
  });

  @override
  State<DrawingBoard> createState() => _DrawingBoardState();
}

class _DrawingBoardState extends State<DrawingBoard> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final cursor = widget.userGraphic.cursor;
    final graphics = widget.userGraphic.visible;
    // 特殊需求支持将手指和线条分开处理
    return CustomPaint(
      painter: _DrawingBoardPainter(
        graphics:
            widget.drawingBoardType != DrawingBoardType.finger ? graphics : [],
        cursor:
            widget.drawingBoardType == DrawingBoardType.line ? null : cursor,
        image:
            widget.drawingBoardType == DrawingBoardType.line
                ? null
                : (AppContext.multiWindow || app.width == 1920)
                ? AppContext.share.fingerSmall
                : AppContext.share.fingerLarge,
        name:
            widget.drawingBoardType == DrawingBoardType.line
                ? null
                : widget.userGraphic.name,
      ),
    );
  }
}

class _DrawingBoardPainter extends CustomPainter {
  final Graphic? cursor;

  final List<Graphic> graphics;
  final ui.Image? image;
  final String? name;

  /// If cursor is not null, then image must not be null
  _DrawingBoardPainter({
    this.cursor,
    required this.graphics,
    this.image,
    this.name,
  }) : assert(
         cursor == null || image != null,
         'If cursor is not null, then image must not be null',
       ),
       super();

  @override
  void paint(Canvas canvas, Size size) {
    for (var graphic in graphics) {
      if (graphic.points.isNotEmpty) {
        final origin = graphic.points.first;
        final path = Path();
        path.moveTo(
          origin.offset.dx * size.width,
          origin.offset.dy * size.height,
        );
        for (var point in graphic.points) {
          path.lineTo(
            point.offset.dx * size.width,
            point.offset.dy * size.height,
          );
        }
        final paint =
            Paint()
              ..color = origin.color
              ..style = PaintingStyle.stroke
              ..strokeWidth = origin.strokeWidth * size.width
              ..strokeCap = StrokeCap.round
              ..strokeJoin = StrokeJoin.round;
        canvas.drawPath(path, paint);
      }
    }
    if (cursor != null && image != null) {
      double realCursorX = cursor!.offset.dx * size.width;
      double realCursorY = cursor!.offset.dy * size.height;
      canvas.drawImage(image!, Offset(realCursorX, realCursorY), Paint());
      const textColor = Colors.white;
      //final fontSize = 74.sp;
      final referSize = image!.width / 4;
      const backgroundColor = Color(0xff2a69e9);
      const paintingStyle = PaintingStyle.fill;
      final width = 2.w;
      final padding = EdgeInsets.symmetric(
        horizontal: referSize,
        vertical: 10.h,
      );
      final style = TextStyle(
        color: textColor,
        fontSize: referSize,
        height: 1.3,
      );
      final span = TextSpan(text: name, style: style);
      final painter = TextPainter(text: span, textDirection: TextDirection.ltr);
      painter.layout(minWidth: 0, maxWidth: size.width);
      double x = realCursorX + (image!.width - painter.width) / 2 + referSize;
      double y = realCursorY + image!.height + 10.h;
      final position = Offset(x, y);
      final backgroundPaint =
          Paint()
            ..color = backgroundColor
            ..style = paintingStyle
            ..strokeWidth = width;
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            x - padding.left,
            y - padding.bottom,
            painter.width + padding.horizontal,
            painter.height + padding.vertical,
          ),
          Radius.circular(16.r),
        ),
        backgroundPaint,
      );
      painter.paint(canvas, position);
    }
  }

  @override
  bool shouldRepaint(_DrawingBoardPainter oldDelegate) {
    return graphics.isEmpty ||
        graphics != oldDelegate.graphics ||
        cursor != oldDelegate.cursor;
  }
}
