import 'dart:math';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/modules/surgery/surgery_create/widgets/number_input.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_create/widgets/surgery_info_selector.widget.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';
import 'surgery_create.controller.dart';

/// 所属模块: surgery
///
/// 手术创建
class SurgeryCreateView extends AppView<SurgeryCreateController> {
  const SurgeryCreateView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return AppBackground(
      child: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: _makeAppBar(context),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        margin: EdgeInsets.only(
          left: 170.w,
          right: 170.w,
          bottom: 56.h,
        ),
        child: ValueListenableBuilder(
          valueListenable: controller.infoSelectState,
          builder: (context, infoSelectState, child) {
            return Row(
              children: [
                SurgeryInfoSelector(
                  items: controller.departmentInfo.datas
                      .map((e) => SurgeryInfoItem(name: e.name))
                      .toList(),
                  onItemSelected: (index) async {
                    await AppPreferences.department
                        .setInt(controller.departmentInfo.datas[index].id);

                    controller.department =
                        controller.departmentInfo.datas[index].id;
                    controller.doctor = null;
                    controller.procedure = null;
                    controller.infoSelectState.value =
                        SurgeryInfoSelectState.doctor;
                  },
                  onTap: () {
                    if (controller.department != null) {
                      controller.infoSelectState.value =
                          SurgeryInfoSelectState.department;
                    }
                  },
                  title: S.current.f_7WldCiLH,
                  isExpanded:
                      infoSelectState == SurgeryInfoSelectState.department,
                  content:
                      controller.departmentMap[controller.department]?.name,
                ),
                SizedBox(width: 24.w),
                SurgeryInfoSelector(
                  items: controller
                      .avaliableSurgeons()
                      .map((e) => SurgeryInfoItem(name: e.name))
                      .toList(),
                  onItemSelected: (index) {
                    controller.doctor = controller.avaliableSurgeons()[index];
                    if (controller.procedure == null) {
                      controller.infoSelectState.value =
                          SurgeryInfoSelectState.procedure;
                    } else {
                      controller.infoSelectState.value =
                          SurgeryInfoSelectState.create;
                    }
                  },
                  onTap: () {
                    controller.infoSelectState.value =
                        SurgeryInfoSelectState.doctor;
                  },
                  title: S.current.f_7Wld4oaN,
                  isExpanded: infoSelectState == SurgeryInfoSelectState.doctor,
                  content:
                      controller.surgeonMap[controller.doctor?.id ?? 0]?.name,
                ),
                SizedBox(width: 24.w),
                SurgeryInfoSelector(
                  crossAxisCount: 3,
                  items: controller
                      .avaliableProcedures()
                      .map((e) => SurgeryInfoItem(
                          name: e.name,
                          imageBg: e.featureFlags?.isRobotProcedure == true
                              ? R.image.bg_robot().keyName
                              : null,
                          imageMark: e.featureFlags?.isRobotProcedure == true
                              ? R.image.icon_robot().keyName
                              : null))
                      .toList(),
                  onItemSelected: (index) {
                    controller.procedure =
                        controller.avaliableProcedures()[index];
                    controller.infoSelectState.value =
                        SurgeryInfoSelectState.create;
                  },
                  onTap: () {
                    if (controller.doctor != null) {
                      controller.infoSelectState.value =
                          SurgeryInfoSelectState.procedure;
                    }
                  },
                  title: S.current.f_7Wld3WwI,
                  isExpanded:
                      infoSelectState == SurgeryInfoSelectState.procedure,
                  content: controller.procedure?.name ?? "",
                ),
                Visibility(
                  visible: infoSelectState == SurgeryInfoSelectState.create,
                  child: Expanded(
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        if (controller.procedure?.algorithmCode == "LC") {
                          /// lC
                          showCheckLCAndLPD(
                            context,
                            S.current.f_MtReYscF,
                          );
                        } else if (controller.procedure?.algorithmCode ==
                            "LPD") {
                          /// LPD
                          showCheckLCAndLPD(
                            context,
                            S.current.f_MtRe2KtZ,
                          );
                        } else {
                          controller.createSurgery(type: false);
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.only(left: 24.w),
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: R.image.create_surgery_background(),
                            fit: BoxFit.cover,
                          ),
                          border: Border.all(
                            color: const Color(0x8AD9EBF6),
                            width: 2.r,
                          ),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              S.current.f_43uAY8UG,
                              style: TextStyle(
                                fontSize: 120.r,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            Transform.rotate(
                              angle: pi,
                              child: Icon(
                                Icons.arrow_back_ios_new_rounded,
                                size: 86.r,
                                color: Colors.white54,
                              ),
                            ),
                            Transform.rotate(
                              angle: pi,
                              child: Icon(
                                Icons.arrow_back_ios_new_rounded,
                                size: 86.r,
                                color: Colors.white70,
                              ),
                            ),
                            Transform.rotate(
                              angle: pi,
                              child: Icon(
                                Icons.arrow_back_ios_new_rounded,
                                size: 86.r,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: _makeAppBar(context),
      body: Center(child: Text(error)),
    );
  }

  Widget? onLoading(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: _makeAppBar(context),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget? onEmpty(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: _makeAppBar(context),
      body: const Center(
        child: Text('There is nothing'),
      ),
    );
  }

  AppBar _makeAppBar(BuildContext context) {
    return AppBar(
      toolbarHeight: 180.h,
      backgroundColor: Colors.transparent,
      centerTitle: true,
      leadingWidth: 480.w,
      leading: Padding(
        padding: EdgeInsets.only(left: 170.w),
        child: Button(
          betweenSpace: 24.w,
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            size: 56.r,
            color: Colors.white,
          ),
          label: Text(
            S.current.f_7VB8izF1,
            style: TextStyle(
              color: Colors.white,
              fontSize: 56.sp,
              height: 78 / 56,
            ),
          ),
          onPressed: context.pop,
        ),
      ),
      title: ValueListenableBuilder(
        valueListenable: controller.infoSelectState,
        builder: (context, value, child) {
          if (!controller.state.isSuccess) {
            return const SizedBox.shrink();
          }
          return Text(
            value!.displayName,
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 80.sp,
              height: 112 / 80,
            ),
          );
        },
      ),
      actions: [
        if (controller.state.isSuccess)
          NumberInput(
            text: controller.admissionNumber,
            onConfirm: (text) {
              controller.admissionNumber = text;
            },
            onClear: () {
              controller.admissionNumber = null;
            },
          ),
      ],
    );
  }

  /// 开始手术弹窗提示 - LC / LPD 提示
  void showCheckLCAndLPD(BuildContext context, String message) {
    Modal(
      title: S.current.f_MtReXqLC,
      kind: ModalKind.dialog,
      type: ModalType.info,
      cancelText: S.current.f_MtReCyNU,
      confirmText: S.current.f_MtRewR6a,
      message: message,
      onConfirm: () async {
        context.pop();
        controller.createSurgery(type: true);
      },
      onClose: () {
        context.pop();
      },
      onCancel: () {
        context.pop();
        controller.createSurgery(type: false);
      },
    ).show(context, barrierDismissible: false);
  }
}
