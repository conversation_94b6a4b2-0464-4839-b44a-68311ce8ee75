import 'dart:convert';
import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/apis/http/device.api.dart';
import 'package:surgsmart/src/apis/http/live.api.dart';
import 'package:surgsmart/src/apis/http/room.api.dart';
import 'package:surgsmart/src/apis/http/surgery.api.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';
import 'package:surgsmart/src/models/http/org.model.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:surgsmart/src/modules/file/file/data_sync_manager.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/db_utils.dart';

/// 手术信息选择状态
enum SurgeryInfoSelectState {
  department,
  doctor,
  procedure,
  create,
}

extension SurgeryInfoSelectStateName on SurgeryInfoSelectState {
  String get displayName {
    switch (this) {
      case SurgeryInfoSelectState.department:
        return S.current.f_7Wld6Nlm;
      case SurgeryInfoSelectState.doctor:
        return S.current.f_7WldJXIk;
      case SurgeryInfoSelectState.procedure:
        return S.current.f_7WldHM42;
      case SurgeryInfoSelectState.create:
        return "";
    }
  }
}

/// 所属模块: surgery
///
/// 手术创建
class SurgeryCreateController extends AppController with StateMixin<ApiModel> {
  SurgeryCreateController(super.key, super.routerState);

  /// 手术信息选择状态
  final infoSelectState = SurgeryInfoSelectState.department.nullableNotifier;

  late final OrgListInfo departmentInfo;
  late final Map<int, OrgInfo> departmentMap;

  late final OrgProcedureListInfo procedureInfo;
  late final Map<int, OrgProcedureInfo> procedureMap;

  late final OrgSurgeonListInfo surgeonInfo;
  late final Map<int, OrgSurgeonInfo> surgeonMap;

  /// 科室
  int? department;

  /// 医生
  OrgSurgeonInfo? doctor;

  /// 术式
  OrgProcedureInfo? procedure;

  /// 住院号
  String? admissionNumber;

  @override
  void onInit() {}

  @override
  void onReady() async {
    try {
      if (AppContext.share.networkConnected.value) {
        await loadData();
      } else {
        await readPreferences();
      }
      readSelectedDepartment();
      update(LoadState.success(ApiModel()));
    } catch (error) {
      update(LoadState.failure(error.toString()));
    }

    checkLastSurgery();
  }

  @override
  void onClose() {
    infoSelectState.dispose();
  }

  Future<void> loadData() async {
    List<dynamic> results = await Future.wait([
      HttpDeviceApi<OrgListInfo>.currentOrg().request(),
      HttpDeviceApi<OrgSurgeonListInfo>.currentOrgSurgeon().request(),
      HttpDeviceApi<OrgProcedureListInfo>.currentOrgProcedure().request()
    ]);
    departmentInfo = results[0];
    surgeonInfo = results[1];
    procedureInfo = results[2];
    app.logW('sp set start ${procedureInfo.toMap()}');

    // 缓存数据到本地
    writePreferences();
  }

  List<OrgSurgeonInfo> avaliableSurgeons() {
    return surgeonInfo.datas
        .where((user) => user.medicalOrganizationIds.contains(department))
        .toList();
  }

  List<OrgProcedureInfo> avaliableProcedures() {
    return procedureInfo.datas
        .where((dp) => dp.medicalOrganizationId == department)
        .toList();
  }

  Future<void> readPreferences() async {
    departmentInfo = OrgListInfo().initWith(
      jsonDecode(AppPreferences.orgInfo.stringValue ?? '{}'),
    );
    surgeonInfo = OrgSurgeonListInfo().initWith(
      jsonDecode(AppPreferences.surgeonInfo.stringValue ?? '{}'),
    );
    procedureInfo = OrgProcedureListInfo().initWith(
      jsonDecode(AppPreferences.procedureInfo.stringValue ?? '{}'),
    );
  }

  Future<void> writePreferences() async {
    await AppPreferences.orgInfo.setString(jsonEncode(departmentInfo.toMap()));
    await AppPreferences.surgeonInfo.setString(jsonEncode(surgeonInfo.toMap()));
    await AppPreferences.procedureInfo.setString(
      jsonEncode(procedureInfo.toMap()),
    );

    app.logW('sp set $procedureInfo');
  }

  var shouldResponse = true;
  Future<void> createSurgery({bool type = true}) async {
    if (!shouldResponse) return;

    if (doctor == null) {
      throw Exception(S.current.f_7WldJXIk);
    }

    if (procedure == null) {
      throw Exception(S.current.f_7WldHM42);
    }
    update(LoadState.loading());

    final data =
        OfflineSurgeryData()
          ..orgId = department!
          ..surgeonId = doctor!.id
          ..procedureId = procedure!.id
          ..admissionNumber = admissionNumber
          ..resolution = (AppPreferences.resolution.intValue ?? 1080).toString()
          ..createTime = DateTime.now();

    final recentRecord = await DbUtils.instance.queryRecentSurgeryRecord();
    if (recentRecord != null) {
      data.localSurgeryId = recentRecord.localSurgeryId + 1;
    } else {
      data.localSurgeryId = 20006;
    }

    await DbUtils.instance.put([data]);

    DbUtils.instance.currentSurgeryInfo = data;

    shouldResponse = false;

    if (AppContext.share.networkConnected.value) {
      try {
        await DataSyncManager().createRemoteSurgery(data);
      } catch (e) {
        app.logE(e.toString());
      }
    }

    await AppPreferences.enableAdvanceAi.setBool(type);

    // 打开手术室
    app.openInner(
      GoPaths.surgeryRoom,
      arguments: {"enableAdvanceAi": type},
      isReplaceCurrent: true,
    );
    shouldResponse = true;
  }

  void readSelectedDepartment() {
    departmentMap = Map.fromEntries(
      departmentInfo.datas.map((e) => MapEntry(e.id, e)),
    );
    surgeonMap = Map.fromEntries(
      surgeonInfo.datas.map((e) => MapEntry(e.id, e)),
    );
    procedureMap = Map.fromEntries(
      procedureInfo.datas.map((e) => MapEntry(e.id, e)),
    );

    department = AppPreferences.department.intValue;
    if (department != null && departmentMap.containsKey(department)) {
      infoSelectState.value = SurgeryInfoSelectState.doctor;
    }
  }

  //检查确保上个手术结束
  Future<void> checkLastSurgery() async {
    if (!AppContext.share.networkConnected.value) return;

    final lastSurgery = await DbUtils.instance.queryRecentSurgeryRecord();
    if (lastSurgery == null) return;
    if (lastSurgery.endTime == null) {
      lastSurgery.endTime = DateTime.now();
      DbUtils().put([lastSurgery]);
    }
    if (lastSurgery.remoteSurgeryId != null) {
      //查询手术信息
      SurgeryInfo surgeryInfo =
          await HttpSurgeryApi<SurgeryInfo>.info(
            surgeryId: lastSurgery.remoteSurgeryId!,
          ).request();

      if (surgeryInfo.status == 20) {
        if (surgeryInfo.liveStatus == 20) {
          await HttpLiveApi.stop(
            deviceId: AppContext.share.authorizeInfo.deviceId,
          ).request();
        }
        await HttpRoomApi.stop(
          deviceId: AppContext.share.authorizeInfo.deviceId,
        ).request();
      }
    }
  }
}
