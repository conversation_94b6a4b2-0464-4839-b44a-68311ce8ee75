import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/widgets/click_animation.widget.dart';

class SurgeryInfoItem {
  final String name;
  final String? imageMark;
  final String? imageBg;

  const SurgeryInfoItem({
    required this.name,
    this.imageMark,
    this.imageBg,
  });
}

class SurgeryInfoSelector extends StatelessWidget {
  final List<SurgeryInfoItem> items;
  final void Function(int index) onItemSelected;
  final VoidCallback onTap;
  final bool isExpanded;
  final String title;
  final String? content;
  final int crossAxisCount;

  const SurgeryInfoSelector({
    super.key,
    required this.items,
    required this.onItemSelected,
    required this.onTap,
    required this.isExpanded,
    required this.title,
    this.content,
    this.crossAxisCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    double minWidth = 377.w;
    double expendedWidth = 3840.w - 388.w - minWidth * 2;
    double paddingSize = 64.r;
    double gridAxisSpacing = 48.r;
    double gridCellWidth = (expendedWidth -
            2 * paddingSize -
            (crossAxisCount - 1) * gridAxisSpacing) /
        crossAxisCount;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isExpanded ? expendedWidth : minWidth,
      curve: Curves.fastOutSlowIn,
      child: isExpanded
          ? Container(
              decoration: BoxDecoration(
                color: const Color(0xFF121820),
                border: Border.all(
                  color: const Color(0xFF0EC6D2),
                  width: 8.r,
                ),
                borderRadius: BorderRadius.circular(20.r),
              ),
              padding: EdgeInsets.all(paddingSize),
              child: ScrollConfiguration(
                behavior: const MaterialScrollBehavior().copyWith(
                  physics: const BouncingScrollPhysics(),
                  dragDevices: {
                    PointerDeviceKind.touch,
                    PointerDeviceKind.mouse,
                  },
                ),
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    mainAxisSpacing: gridAxisSpacing,
                    crossAxisSpacing: gridAxisSpacing,
                    crossAxisCount: crossAxisCount,
                    childAspectRatio: gridCellWidth / 213.h,
                  ),
                  itemCount: items.length,
                  itemBuilder: (BuildContext context, int index) {
                    return ClickAnimateContainer(
                      height: 213.h,
                      width: gridCellWidth,
                      onTap: () => onItemSelected(index),
                      tapDownColor: const Color(0x330EC6D2),
                      child: _ItemCard(items[index]),
                    );
                  },
                ),
              ),
            )
          : GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: onTap,
              child: Container(
                width: minWidth,
                padding: EdgeInsets.symmetric(vertical: 100.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF3C4657),
                      if (content?.isNotEmpty == true)
                        const Color(0xFF0E2F4B)
                      else
                        const Color(0xFF3C4657),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  border: Border.all(
                    color: const Color(0x8AD9EBF6),
                    width: 2.r,
                  ),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (content?.isNotEmpty == true)
                      Expanded(
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.all(24.r),
                            child: Text(
                              content!,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 64.sp,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    Text(
                      title,
                      style: TextStyle(
                        color: const Color(0xCCFFFFFF),
                        fontSize: 56.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}

class _ItemCard<T> extends StatelessWidget {
  final SurgeryInfoItem surgeryInfoItem;

  const _ItemCard(this.surgeryInfoItem);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 213.h,
      width: double.infinity,
      child: Stack(
        children: [
          Container(
            height: 213.h,
            width: double.infinity,
            padding: EdgeInsets.all(10.r),
            decoration: BoxDecoration(
              color: const Color(0x14FFFFFF),
              borderRadius: BorderRadius.circular(12.r),
              image: surgeryInfoItem.imageBg == null
                  ? null
                  : DecorationImage(
                      image: AssetImage(
                      surgeryInfoItem.imageBg!,
                    )),
            ),
            child: Center(
              child: Text(
                surgeryInfoItem.name,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 64.sp,
                  height: 90 / 64,
                ),
              ),
            ),
          ),
          if (surgeryInfoItem.imageMark != null)
            Positioned(
              top: 0,
              right: 0,
              width: 126.w,
              height: 70.h,
              child: Image.asset(
                surgeryInfoItem.imageMark!,
              ),
            ),
        ],
      ),
    );
  }
}
