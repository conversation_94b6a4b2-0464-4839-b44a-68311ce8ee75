import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/http/version.mode.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 系统版本
class VersionController extends AppController with StateMixin<VersionInfo> {
  VersionController(super.key, super.routerState);

  VersionData? versionData;
  VersionInfo? versionInfo;
  bool versionTouch = false;
  bool isShowVersions = false;
  String aiVersion = "";

  @override
  void onInit() {}

  @override
  void onReady() {
    // HttpUpgradeApi<VersionInfo>.checkVersion()
    //     .request()
    //     .then((value) {
    //       versionInfo = value;
    //       update(LoadState.success(value));
    //     })
    //     .onError((error, stackTrace) {
    //       update(LoadState.failure(S.current.f_N8UjXcev));
    //     });

    // HttpUpgradeApi<VersionData>.currentVersions()
    //     .request()
    //     .then((value) {
    //       versionData = value;
    //     })
    //     .onError((error, stackTrace) {
    //       app.logE("获取当前版本失败：$error,$stackTrace");
    //     });

    update(
      LoadState.success(
        VersionInfo()
          ..hasNewVersion = false
          ..version = app.packageInfo.version,
      ),
    );
  }

  @override
  void onClose() {}

  /// 展示更新弹窗
  void showSystemUpdateModal() {
    if (state.value == null) {
      return;
    }
    Modal(
      title: S.current.f_9Tx3J3nv,
      kind: ModalKind.dialog,
      type: ModalType.info,
      cancelText: state.value!.isForce ? null : S.current.f_9Tx3u1Ya,
      confirmText: S.current.f_9Tx3x473,
      closable: false,
      message:
          state.value!.isForce
              ? '检测到新版本${state.value!.newVersion},更新后才能继续使用，请点击更新'
              : StringUtil.replaceInterpolation(S.current.f1_9Tx3CRSk, [
                state.value!.newVersion,
              ]),
      onConfirm: () {
        app.context.pop();
        app.openInner(GoPaths.systemUpdate, arguments: state.value);
      },
      onCancel: () {
        app.context.pop();
      },
    ).show(context!, barrierDismissible: false);
  }

  /// 展示更新弹窗
  void showCurrentVersions() async {
    // if (versionData == null) {
    //   ToastUtils.showToast(message: "版本明细获取失败");
    //   return;
    // }
    if (isShowVersions) return;
    aiVersion = await DeviceCmd.share.getAlgorithmVersion();
    Future.delayed(Duration(seconds: 5)).then((val) {
      if (versionTouch) {
        Modal(
          title: "版本明细",
          kind: ModalKind.dialog,
          type: ModalType.info,
          confirmText: S.current.f_9KZD5TVS,
          child: SingleChildScrollView(
            child: Text(getVersionData(), style: TextStyle(fontSize: 46.sp)),
          ),
          onConfirm: () {
            app.context.pop();
            isShowVersions = false;
          },
          onClose: () {
            app.context.pop();
            isShowVersions = false;
          },
        ).show(context!, barrierDismissible: false);
      }
    });
  }

  String getVersionData() {
    StringBuffer versions = StringBuffer();
    versions.writeln(
      "App Version:${app.packageInfo.version}(${app.packageInfo.buildNumber})",
    );
    versions.writeln("AI Version:$aiVersion");

    // versionData?.datas.forEach((version) {
    //   versions.writeln(
    //     "${version.name}:${version.version}(build ${version.build})",
    //   );
    // });
    return versions.toString();
  }
}
