import 'dart:convert';

import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/apis/app_config.dart';
import 'package:surgsmart/src/apis/http/auth.api.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/toast.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 域名设置
class ServiceController extends AppController with StateMixin<ApiModel> {
  ServiceController(super.key, super.routerState);

  final isEdit = false.notifier;

  final checkNetwork = false.notifier;

  String serviceUrl = '';

  @override
  void onInit() {
    update(LoadState.success(ApiModel()));
  }

  bool verifyServiceUrl(String url) {
    final regex = RegExp(
      r'^https?://' // 协议
      r'(?:' // 域名或IP开始
      r'([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}' // 域名规则
      r'|' // 或
      r'(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])' // IPv4规则
      r'(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}'
      r')' // 域名或IP结束
      r'(?::\d+)?' // 端口
      r'(?:/.*)?$', // 路径
      caseSensitive: false,
    );
    return regex.hasMatch(url);
  }

  Future<bool> initAuthAndServiceConfig() async {
    HttpApiType.surgsmart
        .updateBaseUrl(AppPreferences.privateServerUrl.stringValue ?? '');
    AuthorizeInfo authorizeInfo;
    try {
      authorizeInfo = await HttpAuthApi<AuthorizeInfo>.authorize(
        machineCode: AppContext.share.machineCode,
        licenseCode: AppContext.share.licenseCode,
      ).request();
      await AppPreferences.authorizeInfo
          .setString(jsonEncode(authorizeInfo.toMap()));
    } catch (error) {
      String? authorizeJson = AppPreferences.authorizeInfo.stringValue;
      if (authorizeJson == null) {
        ToastUtils.showToast(message: '授权失败, 请联系售后服务', type: ToastType.error);
        return false;
      }
      authorizeInfo = AuthorizeInfo().initWith(jsonDecode(authorizeJson));
    }

    HttpApiType.surgsmart.updateHeaders(globalHeaders: {
      "Authorization": "Bearer ${authorizeInfo.accessToken}",
    });
    AppContext.share.authorizeInfo = authorizeInfo;
    checkNetwork.value = true;

    return await AppConfig.initConfig();
  }

  @override
  void onReady() {}

  @override
  void onClose() {}
}
