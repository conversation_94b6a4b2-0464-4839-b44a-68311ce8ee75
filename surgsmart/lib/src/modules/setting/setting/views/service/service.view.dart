import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/numeric_keypad_modal.widget.dart';
import 'service.controller.dart';
import 'package:go_router/go_router.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 域名设置
class ServiceView extends AppView<ServiceController> {
  const ServiceView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return controller.build(
      onSuccess: onSuccess,
      onFailure: onFailure,
      onLoading: onLoading,
      onEmpty: onEmpty,
    );
  }

  // 键盘输入
  void showKeypadModal({
    required Function(String) onConfirm,
    String? text,
    String? placeholder,
  }) {
    KeypadModal(
      text: text,
      placeholder: placeholder,
      keypadType: KeypadType.fullKey,
      onConfirm: (input) {
        controller.context?.pop();
        onConfirm(input);
      },
    ).show(controller.context!, barrierDismissible: false);
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return ValueListenableBuilder(
      valueListenable: controller.isEdit,
      builder: (context, isEdit, child) {
        return Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "域名或IP",
                  style: TextStyle(fontSize: 64.sp, color: Colors.white),
                ),
                Spacer(),
                Button(
                  onPressed: () {
                    if (isEdit) {
                      controller.isEdit.value = false;
                    } else {
                      // todo 检测网络
                      controller.checkNetwork.value = true;
                    }
                  },
                  label: Container(
                    width: 200.w,
                    height: 90.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      border: Border.all(width: 3.w, color: Colors.white),
                    ),
                    child: Text(
                      isEdit ? '取消' : '检测网络',
                      style: TextStyle(fontSize: 40.sp, color: Colors.white),
                    ),
                  ),
                ),
                40.horizontalSpace,
                Button(
                  onPressed: () async {
                    if (isEdit) {
                      if (await AppPreferences.privateServerUrl.setString(
                        controller.serviceUrl,
                      )) {
                        ToastUtils.showToast(message: '已保存域名设置');
                        controller.isEdit.value = false;
                        controller.initAuthAndServiceConfig();
                      } else {
                        ToastUtils.showToast(message: '域名保存失败');
                      }
                    } else {
                      controller.isEdit.value = true;
                      showKeypadModal(
                        text: AppPreferences.privateServerUrl.stringValue,
                        onConfirm: (String value) {
                          if (!controller.verifyServiceUrl(value)) {
                            ToastUtils.showToast(
                              message: '请输入正确的域名或IP',
                              type: ToastType.error,
                            );
                            return;
                          }
                          controller.serviceUrl = value;
                          controller.update(LoadState.success(ApiModel()));
                        },
                      );
                    }
                  },
                  label: Container(
                    width: 200.w,
                    height: 90.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0xB25CF5EB),
                          const Color(0xB256B3E3),
                        ],
                      ),
                    ),
                    child: Text(
                      isEdit ? '保存' : '设置',
                      style: TextStyle(fontSize: 40.sp, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
            67.verticalSpace,
            isEdit
                ? GestureDetector(
                  onTap: () {
                    showKeypadModal(
                      text: AppPreferences.privateServerUrl.stringValue,
                      onConfirm: (String value) {
                        if (!controller.verifyServiceUrl(value)) {
                          ToastUtils.showToast(
                            message: '请输入正确的域名或IP',
                            type: ToastType.error,
                          );
                          return;
                        }
                      },
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    height: 150.h,
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(
                      horizontal: 30.w,
                      vertical: 17.h,
                    ),
                    decoration: BoxDecoration(
                      color: Color(0xFF121820),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(width: 4.w, color: Colors.white),
                    ),
                    child: Text(
                      controller.serviceUrl,
                      style: TextStyle(fontSize: 48.sp, color: Colors.white),
                    ),
                  ),
                )
                : ValueListenableBuilder(
                  valueListenable: controller.checkNetwork,
                  builder: (context, checkNetwork, child) {
                    return FutureBuilder(
                      future:
                          checkNetwork
                              ? AppContext.share.netWorkRequest(
                                refreshOptions: true,
                              )
                              : Future.value(true),
                      builder: (context, snapshot) {
                        if (checkNetwork &&
                            snapshot.connectionState == ConnectionState.done) {
                          if (snapshot.data == true) {
                            AppContext.share.networkConnected.value = true;
                          }
                          Future.delayed(Duration(seconds: 1)).then((val) {
                            controller.checkNetwork.value = false;
                          });
                        }
                        return Row(
                          children: [
                            Expanded(
                              child: Text(
                                AppPreferences.privateServerUrl.stringValue ??
                                    '',
                                style: TextStyle(
                                  fontSize: 48.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(top: 6),
                                  child:
                                      checkNetwork
                                          ? CupertinoActivityIndicator(
                                            radius: 25.r,
                                            color: const Color(0xFF297BFF),
                                          )
                                          : Image.asset(
                                            AppContext
                                                    .share
                                                    .networkConnected
                                                    .value
                                                ? R.image
                                                    .icon_success()
                                                    .assetName
                                                : R.image
                                                    .icon_failed()
                                                    .assetName,
                                            width: 50.r,
                                            height: 50.r,
                                          ),
                                ),
                                20.horizontalSpace,
                                Text(
                                  checkNetwork
                                      ? '检测中...'
                                      : AppContext.share.networkConnected.value
                                      ? '可访问'
                                      : '不可访问',
                                  style: TextStyle(
                                    fontSize: 40.sp,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
          ],
        );
      },
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
