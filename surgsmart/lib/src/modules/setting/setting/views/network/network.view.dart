import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/network_device_model.dart';
import 'package:surgsmart/src/modules/setting/setting/views/network/network.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/operation_dropdown.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';
import 'package:surgsmart/src/widgets/numeric_keypad_modal.widget.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 网络设置
class NetworkView extends AppView<NetworkController> {
  const NetworkView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return controller.build(
      onSuccess: onSuccess,
      onFailure: onFailure,
      onLoading: onLoading,
      onEmpty: onEmpty,
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return ValueListenableBuilder(
      valueListenable: controller.type,
      builder: (context, type, child) {
        return Row(
          children: [
            Column(
              children:
                  NetWorkType.labels.keys.map((type) {
                    return GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        controller.type.value = type;
                      },
                      child: tabItem(type),
                    );
                  }).toList(),
            ),
            Expanded(
              child: Container(
                height: double.infinity,
                width: double.infinity,
                padding: EdgeInsets.only(right: 70.w, left: 70.w, top: 40.h),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: getContentByType(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget tabItem(NetWorkType type, {int bottomMargin = 0}) {
    String? selectedNetwork = AppPreferences.selectedNetwork.stringValue;
    return Container(
      height: 186.h,
      width: 446.w,
      margin: EdgeInsets.only(bottom: bottomMargin.h),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color:
            type == controller.type.value
                ? Colors.white.withValues(alpha: 0.15)
                : Colors.transparent,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (selectedNetwork == type.name) ...[
            Image.asset(R.image.icon_selected().assetName),
            13.horizontalSpace,
          ],
          Text(
            NetWorkType.labels[type]!,
            style: TextStyle(
              fontSize: 64.sp,
              color:
                  selectedNetwork == type.name
                      ? Color(0xFF23D9BD)
                      : type == controller.type.value
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget getContentByType() {
    return ValueListenableBuilder(
      valueListenable: controller.type,
      builder: (context, type, child) {
        return ValueListenableBuilder(
          valueListenable: controller.networkState[type]!.isOpen,
          builder: (context, isOpen, child) {
            return Column(
              children: [
                OperationDropdown(
                  title: NetWorkType.labels[controller.type.value]!,
                  switchState: isOpen,
                  decoration: BoxDecoration(),
                  titleRowClickable: false,
                  selfControlState: false,
                  switchScale: 2.2,
                  switchChange: (value) {
                    if (value) {
                      if (AppPreferences.selectedNetwork.stringValue ==
                          NetWorkType.none.name) {
                        controller.switchNetwork(controller.type.value);
                      } else {
                        switchNetworkHintDialog();
                      }
                    } else {
                      controller.switchNetwork(NetWorkType.none);
                    }
                  },
                ),
                40.verticalSpace,
                Expanded(
                  child:
                      isOpen == true
                          ? getSettingView(type)
                          : getEmptyView(type, disable: isOpen == null),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget getSettingView(NetWorkType type) {
    if (type == NetWorkType.gsm) {
      return ValueListenableBuilder(
        valueListenable: controller.getSelectedDeviceNotifier(type: type),
        builder: (context, selectedDevice, child) {
          return gsmSettingView(selectedDevice?.mobileNetWorkInfo);
        },
      );
    }
    //有线列表给初始默认选中赋值
    if (type == NetWorkType.ethernet) {
      getWiredNetworkList();
    }
    return ValueListenableBuilder(
      valueListenable: controller.getSelectedDeviceNotifier(type: type),
      builder: (context, selectedDevice, child) {
        var networkList =
            type == NetWorkType.ethernet
                ? getWiredNetworkList()
                : getWifiNetworkList();
        return Row(
          children: [
            SizedBox(width: 869.w, child: networkList),
            Container(
              width: 2.w,
              height: double.infinity,
              margin: EdgeInsets.only(left: 60.w, right: 60.w, bottom: 26.h),
              color: Colors.white.withValues(alpha: 0.2),
            ),
            Expanded(
              child:
                  selectedDevice == null ||
                          (type == NetWorkType.wifi &&
                              !controller.isSavedWifi(
                                selectedDevice.connection,
                              ))
                      ? getEmptyView(
                        type,
                        isWiredCheck: type == NetWorkType.ethernet,
                      )
                      : getSettingInfoView(networkDevice: selectedDevice),
            ),
          ],
        );
      },
    );
  }

  Widget getEmptyView(
    NetWorkType type, {
    bool isWiredCheck = false,
    bool disable = false,
  }) {
    String assetsName;
    String tip;
    String name = NetWorkType.labels[controller.type.value]!;
    if (type == NetWorkType.wifi) {
      assetsName = R.svg.asset.wifi_disconnected.keyName;
      tip = disable ? '暂未开通$name' : '$name暂未连接';
    } else if (type == NetWorkType.gsm) {
      assetsName = R.svg.asset.cellular_disconnected.keyName;
      tip = disable ? '暂未开通$name' : '$name已关闭';
    } else {
      assetsName = R.svg.asset.wired_disconnected.keyName;
      tip =
          disable
              ? '暂未开通$name'
              : isWiredCheck
              ? '请检查网线'
              : '$name已关闭';
    }
    return Column(
      children: [
        120.verticalSpace,
        SvgPicture.asset(assetsName, height: 160.r, width: 160.r),
        SizedBox(height: 40.h),
        Text(tip, style: TextStyle(fontSize: 48.sp, color: Colors.white)),
      ],
    );
  }

  Widget getWiredNetworkList() {
    return SingleChildScrollView(
      child: Column(
        children:
            AppContext.share.networkDevices
                .where((d) {
                  return d.type == NetWorkType.ethernet.name &&
                      d.device.startsWith('en');
                })
                .map((dev) {
                  //默认将第一个可用的网络设备选中
                  if (controller
                              .getSelectedDeviceNotifier(
                                type: NetWorkType.ethernet,
                              )
                              .value ==
                          null &&
                      dev.isAvailable()) {
                    controller
                        .getSelectedDeviceNotifier(type: NetWorkType.ethernet)
                        .value = dev;
                  }
                  return GestureDetector(
                    onTap: () {
                      if (!dev.isAvailable()) return;
                      controller
                          .getSelectedDeviceNotifier(type: NetWorkType.ethernet)
                          .value = dev;
                    },
                    child: wiredNetworkItem(
                      name: dev.device,
                      isConnected: dev.isAvailable(),
                      isSelected:
                          dev.isAvailable() &&
                          dev.device ==
                              controller
                                  .networkState[NetWorkType.ethernet]!
                                  .selectedDevice
                                  .value
                                  ?.device,
                    ),
                  );
                })
                .toList(),
      ),
    );
  }

  Widget getWifiNetworkList() {
    return ValueListenableBuilder(
      valueListenable: controller.wifiList,
      builder: (context, wifiList, child) {
        if (wifiList.isEmpty) {
          return Padding(
            padding: EdgeInsets.only(bottom: 180.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(color: Colors.white),
                20.horizontalSpace,
                Text('正在扫描WiFi网络...', style: TextStyle(fontSize: 48.sp)),
              ],
            ),
          );
        }
        return SingleChildScrollView(
          child: Column(
            children:
                wifiList.map((device) {
                  return GestureDetector(
                    onTap: () {
                      controller.getSelectedDeviceNotifier().value = device.clone();
                    },
                    child: wifiNetworkItem(
                      name: device.connection,
                      isConnected: device.isConnected(),
                      isSelected:
                          device.connection ==
                          controller.getSelectedDeviceNotifier().value?.connection,
                      bottomMargin: 30.h,
                      onConnect: () {
                        if (device.isConnected()) return;
                        controller.connectWifiDevice(device);
                      },
                    ),
                  );
                }).toList(),
          ),
        );
      },
    );
  }

  Widget getSettingInfoView({required NetworkDevice networkDevice}) {
    bool isEdit = networkDevice.extensions?['isEdit'] == true;
    bool clickable = networkDevice.extensions?['clickable'] == true;

    return FutureBuilder<NetworkConfig?>(
      future: getNetworkConfig(networkDevice),
      builder: (context, snapshot) {
        NetworkConfig tempConfig =
            controller
                .networkState[controller.type.value]!
                .selectedDevice
                .value!
                .config ??
            NetworkConfig();

        NetworkConfig config =
            isEdit ? tempConfig : snapshot.data?.clone() ?? NetworkConfig();

        //关联网络详细信息，用以手动修改配置暂存区
        controller.getSelectedDeviceNotifier().value?.config = config;

          return Column(
            children: [
            Row(
              children: [
                Expanded(
                  child: FutureBuilder<String>(
                    future: controller.getConnectionNameByDeviceName(networkDevice),
                    builder: (context, snapshot) {
                      return Text(
                        snapshot.data ?? '--',
                        style: TextStyle(
                          fontSize: 48.sp,
                          color: Colors.white,
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    },
                  ),
                ),
                if (networkDevice.type == NetWorkType.wifi.name &&
                    controller.isSavedWifi(networkDevice.connection)) ...[
                  Button(
                    onPressed: () async {
                      removeNetworkDialog(() async {
                        if (networkDevice.isConnected()) {
                          controller.showLoadingDialog("正在断开并移除该WiFi连接...");
                          await controller.forgetWifi(networkDevice.connection);
                          controller.hideLoadingDialog();
                        } else {
                          controller.forgetWifi(networkDevice.connection);
                        }
                      });
                    },
                    label: Container(
                      width: 200.w,
                      height: 90.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.r),
                        border: Border.all(width: 3.w, color: Colors.white),
                      ),
                      child: Text(
                        '移除网络',
                        style: TextStyle(fontSize: 40.sp, color: Colors.white),
                      ),
                    ),
                  ),
                  40.horizontalSpace,
                ],
                Button(
                  onPressed: () async {
                    if (isEdit) {
                      controller.setEditMode(isEdit: false, clickable: false);
                    } else {
                      //检查网络是否可用
                      if (controller
                              .networkState[controller.type.value]!
                              .isOpen
                              .value !=
                          true) {
                        ToastUtils.showToast(
                          message: '请先打开并连接当前网络',
                        );
                        return;
                      }
                      showNetworkCheckDialog();
                    }
                  },
                  label: Container(
                    width: 200.w,
                    height: 90.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      border: Border.all(width: 3.w, color: Colors.white),
                    ),
                    child: Text(
                      isEdit ? '取消' : '检测网络',
                      style: TextStyle(fontSize: 40.sp, color: Colors.white),
                    ),
                  ),
                ),
                40.horizontalSpace,
                Button(
                  onPressed: () async {
                    if (isEdit) {
                      controller.saveNetworkConfig(networkDevice, tempConfig);
                    } else {
                      controller.setEditMode(
                        isEdit: true,
                        clickable: config.method == NetWorkMethod.manual.name,
                      );
                    }
                  },
                  label: Container(
                    width: 200.w,
                    height: 90.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0xB25CF5EB),
                          const Color(0xB256B3E3),
                        ],
                      ),
                    ),
                    child: Text(
                      isEdit ? '保存' : '设置',
                      style: TextStyle(fontSize: 40.sp, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
            isEdit ? 30.verticalSpace : 50.verticalSpace,
            isEdit
                ? getIpMethodField(config.method)
                : getConfigField(
                  name: "配置IP",
                  value: config.method == NetWorkMethod.auto.name ? '自动' : '手动',
                ),
            getConfigField(
              name: "IP地址",
              value: config.ip.join(','),
              clickable: clickable,
              isEdit: isEdit,
              onInput: (input) {
                if (!tempConfig.isValidIP(input)) {
                  ToastUtils.showToast(
                    message: '请输入正确的IP地址',
                    type: ToastType.error,
                  );
                  return;
                }
                tempConfig.ip.clear();
                tempConfig.ip.add(input);
                Navigator.of(context).pop();
                controller
                    .networkState[controller.type.value]!
                    .selectedDevice
                    .value = networkDevice.clone();
              },
            ),
            getConfigField(
              name: "子网掩码",
              value: config.subnetMask.join(','),
              clickable: clickable,
              isEdit: isEdit,
              onInput: (input) {
                app.logW(
                  "$input: ${!tempConfig.cidrMap.values.contains(input)}",
                );
                if (!tempConfig.isValidIP(input) ||
                    !tempConfig.cidrMap.values.contains(input)) {
                  ToastUtils.showToast(
                    message: '请输入正确的子网掩码',
                    type: ToastType.error,
                  );
                  return;
                }
                tempConfig.subnetMask.clear();
                tempConfig.subnetMask.add(input);
                Navigator.of(context).pop();
                controller
                    .networkState[controller.type.value]!
                    .selectedDevice
                    .value = networkDevice.clone();
              },
            ),
            getConfigField(
              name: "网关",
              value: config.gateway,
              clickable: clickable,
              isEdit: isEdit,
              onInput: (input) {
                if (!tempConfig.isValidIP(input)) {
                  ToastUtils.showToast(
                    message: '请输入正确的网关地址',
                    type: ToastType.error,
                  );
                  return;
                }
                tempConfig.gateway = input;
                Navigator.of(context).pop();
                controller
                    .networkState[controller.type.value]!
                    .selectedDevice
                    .value = networkDevice.clone();
              },
            ),
            getConfigField(
              name: "DNS",
              value: config.dnsServers.join(','),
              clickable: clickable,
              isEdit: isEdit,
              onInput: (input) {
                if (!tempConfig.isValidIP(input)) {
                  ToastUtils.showToast(
                    message: '请输入正确的DNS',
                    type: ToastType.error,
                  );
                  return;
                }
                tempConfig.dnsServers.clear();
                tempConfig.dnsServers.add(input);
                Navigator.of(context).pop();
                controller
                    .networkState[controller.type.value]!
                    .selectedDevice
                    .value = networkDevice.clone();
              },
            ),
          ],
        );
      },
    );
  }

  Widget getIpMethodField(String value) {
    final Icon selectedMode = Icon(
      Icons.radio_button_checked,
      color: Color(0xff23D9BD),
      size: 46,
    );
    final Icon unSelectedMode = Icon(
      Icons.radio_button_unchecked,
      color: Colors.white,
      size: 46,
    );

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 10.h),
      margin: EdgeInsets.only(bottom: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text('配置IP', style: TextStyle(fontSize: 40.sp, color: Colors.white)),
          Spacer(),
          Button(
            onPressed: () {
              controller.getSelectedDeviceNotifier().value?.config?.method =
                  NetWorkMethod.auto.name;
              controller.setEditMode(clickable: false);
            },
            icon:
                value == NetWorkMethod.auto.name
                    ? selectedMode
                    : unSelectedMode,
            betweenSpace: 15,
            label: Text(
              '自动',
              style: TextStyle(fontSize: 40.sp, color: Colors.white),
            ),
          ),
          82.horizontalSpace,
          Button(
            onPressed: () {
              controller.getSelectedDeviceNotifier().value?.config?.method =
                  NetWorkMethod.manual.name;

              controller.setEditMode(clickable: true);
            },
            icon:
                value == NetWorkMethod.manual.name
                    ? selectedMode
                    : unSelectedMode,
            betweenSpace: 15,
            label: Text(
              '手动',
              style: TextStyle(fontSize: 40.sp, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget getConfigField({
    required String name,
    required String value,
    bool clickable = false,
    bool isEdit = false,
    Function(String)? onInput,
  }) {
    return GestureDetector(
      onTap: () {
        if (clickable) {
          controller.showKeypadModal(
            text: value,
            keypadType: KeypadType.decimal,
            placeholder: '请输入$name',
            onConfirm: (text) {
              onInput?.call(text);
            },
          );
        }
      },
      child: Container(
        decoration:
            isEdit
                ? BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12.r),
                )
                : null,
        padding:
            isEdit
                ? EdgeInsets.symmetric(horizontal: 30.w, vertical: 10.h)
                : null,
        margin: EdgeInsets.only(bottom: isEdit ? 12.h : 26.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(name, style: TextStyle(fontSize: 40.sp, color: Colors.white)),
            Text(value, style: TextStyle(fontSize: 40.sp, color: Colors.white)),
          ],
        ),
      ),
    );
  }

  Widget wiredNetworkItem({
    required String name,
    required isConnected,
    required bool isSelected,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 20.h),
      margin: EdgeInsets.only(bottom: 30.h),
      decoration: BoxDecoration(
        color:
            isSelected
                ? const Color(0xff0EC6D2).withValues(alpha: 0.2)
                : Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12.w),
        border: Border.all(
          width: 8.w,
          color: isSelected ? const Color(0xff0EC6D2) : Colors.transparent,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 48.sp,
                    color:
                        isConnected
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.5),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  isConnected ? "已插网线" : "未插网线",
                  style: TextStyle(
                    fontSize: 40.sp,
                    color:
                        isConnected
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
          if (isConnected) Image.asset(R.image.icon_wired_selected().assetName),
        ],
      ),
    );
  }

  Widget wifiNetworkItem({
    required String name,
    required bool isConnected,
    required bool isSelected,
    required VoidCallback onConnect,
    double bottomMargin = 0,
  }) {
    return Container(
      padding: EdgeInsets.only(
        top: 20.h,
        bottom: 20.h,
        left: 40.w,
        right: 20.w,
      ),
      margin: EdgeInsets.only(bottom: bottomMargin),
      decoration: BoxDecoration(
        color:
            isSelected
                ? const Color(0xff0EC6D2).withValues(alpha: 0.2)
                : Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12.w),
        border: Border.all(
          width: 8.w,
          color: isSelected ? const Color(0xff0EC6D2) : Colors.transparent,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Text(
              name,
              style: TextStyle(
                fontSize: 48.sp,
                color: Colors.white,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (isConnected) ...[
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xFF23D9BD),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  width: 24.r,
                  height: 24.r,
                ),
                SizedBox(width: 20.w),
                Text(
                  '已连接',
                  style: TextStyle(fontSize: 40.sp, color: Colors.white),
                ),
                20.horizontalSpace,
              ],
              if (!isConnected)
                Button(
                  onPressed: onConnect,
                  label: Container(
                    width: 140.w,
                    height: 66.h,
                    margin: EdgeInsets.only(left: 20.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      border: Border.all(width: 3.w, color: Colors.white),
                    ),
                    child: Text(
                      '连接',
                      style: TextStyle(fontSize: 40.sp, color: Colors.white),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget gsmSettingView(MobileNetWorkInfo? networkInfo) {
    bool isEdit = controller.isEditMode();
    final Icon selectedMode = Icon(
      Icons.radio_button_checked,
      color: Color(0xff23D9BD),
      size: 46,
    );
    final Icon unSelectedMode = Icon(
      Icons.radio_button_unchecked,
      color: Colors.white,
      size: 46,
    );
    return Column(
      children: [
        Container(
          height: 150.h,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(12.r),
          ),
          padding: EdgeInsets.only(left: 30.w, right: 30.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "接入点",
                style: TextStyle(fontSize: 48.sp, color: Colors.white),
              ),
              70.horizontalSpace,
              GestureDetector(
                onTap: () {
                  if (isEdit) {
                    apnInput(networkInfo);
                  }
                },
                child:
                    (networkInfo == null && !isEdit)
                        ? SizedBox.shrink()
                        : Container(
                          width: 500.w,
                          height: 90.h,
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(
                            horizontal: 30.w,
                            vertical: 17.h,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isEdit
                                    ? Color(0xFF121820)
                                    : Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12.r),
                            border:
                                isEdit
                                    ? Border.all(
                                      width: 4.w,
                                      color: Colors.white,
                                    )
                                    : null,
                          ),
                          child: Text(
                            networkInfo?.apn ?? '',
                            style: TextStyle(
                              fontSize: 40.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
              ),
              Spacer(),
              Button(
                onPressed: () {
                  if (isEdit) {
                    controller.setEditMode(isEdit: false);
                  } else {
                    // todo 检测网络
                    showNetworkCheckDialog();
                  }
                },
                label: Container(
                  width: 200.w,
                  height: 90.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.r),
                    border: Border.all(width: 3.w, color: Colors.white),
                  ),
                  child: Text(
                    isEdit ? '取消' : '检测网络',
                    style: TextStyle(fontSize: 40.sp, color: Colors.white),
                  ),
                ),
              ),
              40.horizontalSpace,
              Button(
                onPressed: () {
                  if (isEdit) {
                    // todo 设置接入点
                    MobileNetWorkInfo? mobileInfo =
                        controller.getSelectedDeviceNotifier(
                          type: NetWorkType.gsm,
                        ).value?.mobileNetWorkInfo;
                    if (mobileInfo == null || mobileInfo.apn.isEmpty) {
                      ToastUtils.showToast(
                        message: '请先配置接入点',
                      );
                      return;
                    }
                    controller.setMobileNetwork();
                    ToastUtils.showToast(message: '已保存设置');

                    controller.setEditMode(isEdit: false);
                  } else {
                    controller.setEditMode(isEdit: true);
                    apnInput(networkInfo);
                  }
                },
                label: Container(
                  width: 200.w,
                  height: 90.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.r),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xB25CF5EB),
                        const Color(0xB256B3E3),
                      ],
                    ),
                  ),
                  child: Text(
                    isEdit ? '保存' : '设置',
                    style: TextStyle(fontSize: 40.sp, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
        30.verticalSpace,
        Container(
          height: 150.h,
          padding: EdgeInsets.only(left: 30.w, right: 30.w),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "选择网络",
                style: TextStyle(fontSize: 48.sp, color: Colors.white),
              ),
              Spacer(),
              Button(
                betweenSpace: 15,
                onPressed: () {
                  controller.setMobileConfig(mode: MobileNetWorkMode.auto);
                },
                icon:
                    networkInfo?.mode == MobileNetWorkMode.auto
                        ? selectedMode
                        : unSelectedMode,
                label: Text(
                  '5G/4G',
                  style: TextStyle(fontSize: 40.sp, color: Colors.white),
                ),
              ),
              80.horizontalSpace,
              Button(
                onPressed: () {
                  controller.setMobileConfig(mode: MobileNetWorkMode.only5G);
                },
                icon:
                    networkInfo?.mode == MobileNetWorkMode.only5G
                        ? selectedMode
                        : unSelectedMode,
                betweenSpace: 15,
                label: Text(
                  '仅5G',
                  style: TextStyle(fontSize: 40.sp, color: Colors.white),
                ),
              ),
              80.horizontalSpace,
              Button(
                onPressed: () {
                  controller.setMobileConfig(mode: MobileNetWorkMode.only4G);
                },
                icon:
                    networkInfo?.mode == MobileNetWorkMode.only4G
                        ? selectedMode
                        : unSelectedMode,
                betweenSpace: 15,
                label: Text(
                  '仅4G',
                  style: TextStyle(fontSize: 40.sp, color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void removeNetworkDialog(VoidCallback onConfirm) async {
    Modal(
      title: '网络移除提示',
      kind: ModalKind.dialog,
      type: ModalType.info,
      cancelText: '取消',
      confirmText: '移除该网络',
      message: '移除后，设备将不再自动连接该网络，再次连接需要重新输入密码，确认移除该无线局域网吗？',
      onConfirm: () {
        controller.context!.pop();
        onConfirm.call();
      },
      onCancel: () {
        controller.context!.pop();
      },
      onClose: () {
        controller.context!.pop();
      },
    ).show(controller.context!);
  }

  void showNetworkCheckDialog() async {
    controller.showLoadingDialog('检测网络中...');
    bool isSuccess = true;
    try {
      isSuccess = await controller.checkNetwork();
    } catch (e) {
      app.logE(e.toString());
      isSuccess = false;
    }
    controller.hideLoadingDialog();
    Modal(
      title: '网络连接提示',
      kind: ModalKind.dialog,
      type: isSuccess ? ModalType.info : ModalType.error,
      cancelText: isSuccess ? null : '返回',
      confirmText: isSuccess ? '好的' : null,
      onConfirm:
          isSuccess
              ? () {
                controller.context!.pop();
              }
              : null,
      onCancel:
          isSuccess
              ? null
              : () {
                controller.context!.pop();
              },
      onClose: () {
        controller.context!.pop();
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            isSuccess
                ? R.image.icon_success().assetName
                : R.image.icon_failed().assetName,
          ),
          30.horizontalSpace,
          Text(
            isSuccess ? '网络已连接' : '网络连接失败',
            style: TextStyle(color: Colors.white, fontSize: 96.sp),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).show(controller.context!);
  }

  void apnInput(MobileNetWorkInfo? networkInfo) {
    controller.showKeypadModal(
      keypadType: KeypadType.fullKey,
      placeholder: "请输入接入点",
      text: networkInfo?.apn,
      onConfirm: (String value) {
        if (value.isEmpty) {
          ToastUtils.showToast(
            message: '接入点不能为空',
            type: ToastType.error,
          );
          return;
        }
        controller.setMobileConfig(apn: value, isSave: false);
        Navigator.of(controller.context!).pop();
      },
    );
  }

  void switchNetworkHintDialog() {
    String hint = '设备仅支持开启一种网络，';
    if (controller.type.value == NetWorkType.ethernet) {
      hint += '开启有线网络将自动关闭Wi-Fi、蜂窝网络，确定开启吗？';
    } else if (controller.type.value == NetWorkType.wifi) {
      hint += '开启Wi-Fi将自动关闭有线网络、蜂窝网络，确定开启吗？';
    } else if (controller.type.value == NetWorkType.gsm) {
      hint += '开启蜂窝网络将自动关闭有线网络、Wi-Fi，确定开启吗？';
    }
    Modal(
      title: '网络切换提示',
      kind: ModalKind.dialog,
      type: ModalType.info,
      message: hint,
      cancelText: '暂不开启',
      confirmText: '确定开启',
      onConfirm: () {
        controller.context!.pop();
        controller.switchNetwork(controller.type.value);
      },
      onCancel: () {
        controller.context!.pop();
      },
    ).show(controller.context!);
  }

  Future<NetworkConfig?> getNetworkConfig(NetworkDevice networkDevice) async {
    String connection = await controller.getConnectionNameByDeviceName(networkDevice);
    return AppContext.share.getNetworkConfig(connection: connection);
  }
}
