import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/http/auth.api.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/toast.dart';

enum ServiceType {
  domain,
  network,
  networkSetting,

  authorization;

  static Map<ServiceType, String> labels = {
    ServiceType.domain: '域名设置',
    ServiceType.network: '网络设置',
    ServiceType.authorization: '认证授权',
  };
}

/// 所属模块: service_setting
///
/// 域名、网络设置
class ServiceSettingController extends AppController with StateMixin<ApiModel> {
  ServiceSettingController(super.key, super.routerState);

  final type = ServiceType.domain.notifier;
  String serviceUrl = 'https://';

  @override
  void onInit() {
    if (routerState?.extra != null) {
      final extra = (routerState?.extra as Map<String, dynamic>);
      type.value = extra['type'] as ServiceType;
    } else {
      //本地部署流程
      // todo 判断machine_code、license是否存在？
      // 如果多次进入此流程，每次重新生成machine_code、license，避免脏数据影响
    }

    update(LoadState.success(ApiModel()));
  }

  bool verifyServiceUrl(String url) {
    final regex = RegExp(
      r'^https?://' // 协议
      r'(?:' // 域名或IP开始
      r'([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}' // 域名规则
      r'|' // 或
      r'(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])' // IPv4规则
      r'(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}'
      r')' // 域名或IP结束
      r'(?::\d+)?' // 端口
      r'(?:/.*)?$', // 路径
      caseSensitive: false,
    );
    return regex.hasMatch(url);
  }

  /// 生成机器码并获取license
  Future<bool> createMachineCodeAndLicense() async {
    final homeDirectory =
        Platform.environment['HOME'] ?? Platform.environment['USERPROFILE'];
    final authDir = "$homeDirectory/.config/surgsmart";
    try {
      // 生成 64 字符的随机十六进制字符串
      final machineCode = generateSecureHexString(64);
      final file = File('$authDir/machine_code');
      await file.parent.create(recursive: true);
      await file.writeAsString(machineCode, flush: true);

      ApiModel apiModel =
          await HttpAuthApi<ApiModel>.getLicenseCode(
            machineCode: machineCode,
          ).request();

      if (apiModel.map?['license'] != null) {
        final file = File('$authDir/license');
        await file.parent.create(recursive: true);
        await file.writeAsString(apiModel.map!['license'], flush: true);
        return true;
      }
      app.logE('getLicenseCode: ${apiModel.map.toString()}');
    } catch (e) {
      app.logE('createMachineCode error: $e');
    }

    return false;
  }

  String generateSecureHexString(int length) {
    if (length % 2 != 0) throw ArgumentError('长度必须为偶数');
    // 生成加密安全的随机字节
    final random = Random.secure();
    final bytes = List<int>.generate(length ~/ 2, (_) => random.nextInt(256));
    // 转换为十六进制并拼接
    return bytes
        .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join('')
        .substring(0, length); // 确保精确长度
  }

  Future<bool> initAuth() async {
    AuthorizeInfo authorizeInfo;
    try {
      await AppContext.share.readLicenseCode();

      authorizeInfo =
          await HttpAuthApi<AuthorizeInfo>.authorize(
            machineCode: AppContext.share.machineCode,
            licenseCode: AppContext.share.licenseCode,
          ).request();
      if (authorizeInfo.accessToken.isEmpty) {
        ToastUtils.showToast(message: '设备未授权', type: ToastType.error);
        return false;
      }
      await AppPreferences.authorizeInfo.setString(
        jsonEncode(authorizeInfo.toMap()),
      );
    } catch (error) {
      ToastUtils.showToast(message: '设备未授权', type: ToastType.error);
      return false;
    }
    return true;
  }

  @override
  void onReady() {}

  @override
  void onClose() {}
}
