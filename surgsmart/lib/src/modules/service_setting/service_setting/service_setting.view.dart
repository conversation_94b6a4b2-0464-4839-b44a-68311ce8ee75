import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/modules/setting/setting/views/network/network.controller.dart';
import 'package:surgsmart/src/modules/setting/setting/views/network/network.view.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';
import '../../../widgets/numeric_keypad_modal.widget.dart';
import 'service_setting.controller.dart';

/// 所属模块: service_setting
///
/// 域名/网络服务设置
class ServiceSettingView extends AppView<ServiceSettingController> {
  const ServiceSettingView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AppBackground(
        child: controller.build(
          onSuccess: onSuccess,
          onFailure: onFailure,
          onLoading: onLoading,
          onEmpty: onEmpty,
        ),
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return ValueListenableBuilder(
      valueListenable: controller.type,
      builder: (context, type, child) {
        return getContentByType(context);
      },
    );
  }

  Widget serviceUrlSetting() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        getHeader(
          title: '域名设置',
          onNext: () async {
            if (!controller.verifyServiceUrl(controller.serviceUrl)) {
              ToastUtils.showToast(
                message: '请输入正确的域名或IP地址',
                type: ToastType.error,
              );
              return;
            }
            //确保部署过程中不受脏数据影响
            await AppPreferences.authorizeInfo.remove();
            await AppPreferences.privateServerUrl.setString(
              controller.serviceUrl,
            );
            // 更新API的基础URL
            HttpApiType.surgsmart.updateBaseUrl(controller.serviceUrl);
            controller.type.value = ServiceType.network;
          },
        ),
        200.verticalSpace,
        SizedBox(
          width: 2420.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '请输入域名或IP，连接服务',
                style: TextStyle(color: Colors.white, fontSize: 72.sp),
              ),
              GestureDetector(
                onTap: () {
                  showKeypadModal(
                    text: controller.serviceUrl,
                    onConfirm: (input) {
                      controller.serviceUrl = input;
                      controller.update(LoadState.success(ApiModel()));
                    },
                  );
                },
                child: Container(
                  width: double.infinity,
                  height: 198.h,
                  margin: EdgeInsets.only(top: 24.h),
                  padding: EdgeInsets.all(46.r),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: Colors.white, width: 2.r),
                    color: Color(0xff121820),
                  ),
                  child: Text(
                    controller.serviceUrl,
                    style: TextStyle(color: Colors.white, fontSize: 72.sp),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  Widget getContentByType(BuildContext context) {
    if (controller.type.value == ServiceType.domain) {
      return serviceUrlSetting();
    } else if (controller.type.value == ServiceType.network ||
        controller.type.value == ServiceType.networkSetting) {
      bool networkSetting = controller.type.value == ServiceType.networkSetting;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          getHeader(
            title: networkSetting ? '' : '请选择并设置网络',
            backText: networkSetting ? '返回' : '上一步',
            onNext:
                networkSetting
                    ? null
                    : () async {
                      showLoadingDialog(
                        tag: createMachineCodeTag,
                        hint: '正在加载请稍后...',
                      );
                      if (!await controller.createMachineCodeAndLicense()) {
                        ToastUtils.showToast(
                          message: '获取授权码失败',
                          type: ToastType.error,
                        );
                      } else {
                        controller.type.value = ServiceType.authorization;
                      }
                      hideLoadingDialog(tag: createMachineCodeTag);
                    },
            onBack: () {
              if (networkSetting) {
                context.pop();
              } else {
                controller.type.value = ServiceType.domain;
              }
            },
          ),
          Expanded(
            child: Container(
              height: double.infinity,
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 64.w, vertical: 48.h),
              margin: EdgeInsets.only(
                left: 154.w,
                right: 154.w.w,
                bottom: 56.h,
              ),
              decoration: BoxDecoration(
                color: const Color(0xff1D2632),
                borderRadius: BorderRadius.circular(24.w),
                border: Border.all(width: 8.w, color: const Color(0xff313E50)),
              ),
              child: NetworkView(
                key: key,
                binding:
                    (key) => NetworkController(
                      key,
                      null,
                      isSystemSetting:
                          controller.type.value == ServiceType.networkSetting,
                    ),
              ),
            ),
          ),
        ],
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 154.w, top: 34.h),
              child: Image(
                image: R.image.logo_variant(),
                fit: BoxFit.cover,
                width: 385.w,
                height: 90.h,
              ),
            ),
          ],
        ),
        200.verticalSpace,
        Text(
          '连接成功,请在管理后台完成设置与授权',
          style: TextStyle(fontSize: 96.sp, color: Colors.white),
        ),
        120.verticalSpace,
        Button(
          onPressed: () async {
            if (await controller.initAuth()) {
              app.openInner(GoPaths.home, isReplaceCurrent: true);
            }
          },
          label: Container(
            width: 840.w,
            height: 200.h,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.r),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [const Color(0xB25CF5EB), const Color(0xB256B3E3)],
              ),
            ),
            child: Text(
              '已授权，启用设备',
              style: TextStyle(
                fontSize: 80.sp,
                color: Colors.white,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ],
    );
  }

  getHeader({
    String? title,
    String? backText,
    Function()? onNext,
    Function()? onBack,
  }) {
    return SizedBox(
      height: 180.h,
      child: Stack(
        children: [
          if (onBack != null)
            Positioned(
              left: 154.w,
              top: 58.h,
              width: 240.w,
              height: 78.h,
              child: Button(
                onPressed: onBack,
                icon: Icon(
                  Icons.arrow_back_ios,
                  size: 56.r,
                  color: Colors.white,
                ),
                foregroundColor: Colors.white,
                label: Text(
                  backText ?? "上一步",
                  style: TextStyle(fontSize: 56.sp, color: Colors.white),
                ),
                betweenSpace: 15.w,
              ),
            ),
          if (title != null)
            Center(
              child: Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 80.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          if (onNext != null)
            Positioned(
              right: 154.w,
              top: 58.h,
              width: 240.w,
              height: 78.h,
              child: Button(
                onPressed: onNext,
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      "下一步",
                      style: TextStyle(fontSize: 56.sp, color: Colors.white),
                    ),
                    15.horizontalSpace,
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 56.r,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 键盘输入
  void showKeypadModal({
    required Function(String) onConfirm,
    String? text,
    String? placeholder,
  }) {
    KeypadModal(
      text: text,
      placeholder: placeholder,
      keypadType: KeypadType.fullKey,
      onConfirm: (input) {
        controller.context?.pop();
        onConfirm(input);
      },
    ).show(controller.context!, barrierDismissible: false);
  }

  final createMachineCodeTag = 'createMachineCodeAndLicense';
  final getAuthTag = 'getAuthTag';

  void hideLoadingDialog({required String tag}) {
    app.removeOverlay(tag: tag);
  }

  void showLoadingDialog({required String tag, required String hint}) {
    if (app.existedOverlay(tag: tag)) {
      return;
    }
    app.addOverlay(
      tag: tag,
      child: Positioned.fill(
        child: Container(
          decoration: BoxDecoration(color: Colors.black.withValues(alpha: 0.6)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CupertinoActivityIndicator(
                radius: 155.r,
                color: const Color(0xFF297BFF),
              ),
              SizedBox(height: 40.h),
              Text(
                hint,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 64.sp),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
