import 'package:app_foundation/app_foundation.dart';
import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart';
import 'package:medias_kit/monitor/monitor_view.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/drawing_board.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/corner_container.widget.dart';
import 'expand_screen.controller.dart';

/// 所属模块: expand_screen
///
/// 扩展屏幕显示
class ExpandScreenView extends AppView<ExpandScreenController> {
  const ExpandScreenView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel data) {
    var monitorView =
        controller.monitorController == null
            ? const SizedBox.shrink()
            : MonitorView(monitor: controller.monitorController!);
    return ValueListenableBuilder(
      valueListenable: controller.statusInfo,
      builder: (context, status, child) {
        return status.surgeryIsStart
            ? Stack(
              children: [
                monitorView,
                Visibility(
                  visible: status.videoIsFuzzy,
                  child: Positioned.fill(
                    child: const SizedBox.shrink().frosted(
                      frostColor: Colors.black,
                      blur: 50,
                      borderRadius: BorderRadius.circular(20.r),
                      padding: const EdgeInsets.symmetric(
                        vertical: double.infinity,
                        horizontal: double.infinity,
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: status.externalVideoDevice != null,
                  child: Positioned(
                    width: 576.w,
                    height: 324.h,
                    bottom: 0,
                    right: 0,
                    child:
                        controller.monitor2Controller == null
                            ? const SizedBox.shrink()
                            : MonitorView(
                              monitor: controller.monitor2Controller!,
                            ),
                  ),
                ),

                if (status.isPlayback)
                  Positioned.fill(
                    child: ValueListenableBuilder(
                      valueListenable: controller.render,
                      builder: (context, value, child) {
                        if (value == null) {
                          return const SizedBox.shrink();
                        }
                        return Container(
                          width: double.infinity,
                          height: double.infinity,
                          color: Colors.black,
                          alignment: Alignment.center,
                          child: Texture(
                            textureId: textureRendererGetTextureId(value),
                          ),
                        );
                      },
                    ),
                  ),

                ValueListenableBuilder(
                  valueListenable: controller.graphics,
                  builder: (context, graphics, child) {
                    /// 专家绘线
                    return Stack(
                      children: [
                        ...controller.graphics.value.map(
                          (graphic) => Positioned.fill(
                            child: DrawingBoard(
                              drawingBoardType: DrawingBoardType.fingerLine,
                              userGraphic: graphic,
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 32.r,
                    vertical: 32.r,
                  ),
                  child: ValueListenableBuilder(
                    valueListenable: AppContext.share.networkConnected,
                    builder: (context, networkConnected, child) {
                      return Row(
                        children: [
                          if (!status.isPlayback)
                            Text(
                              status.procedureName ?? '未知术式',
                              style: TextStyle(
                                fontSize: 40.sp,
                                height: 67 / 52,
                                fontWeight: FontWeight.w500,
                              ),
                            ).frosted(
                              frostColor: const Color(0xFF777777),
                              blur: 10,
                              borderRadius: BorderRadius.circular(10.r),
                              padding: EdgeInsets.symmetric(
                                vertical: 30.h,
                                horizontal: 42.w,
                              ),
                            ),
                          const Spacer(),
                          // 手术协同医生人数
                          Visibility(
                            visible:
                                networkConnected && status.doctors.isNotEmpty,
                            child: Padding(
                              padding: EdgeInsets.only(right: 18.w),
                              child: CornerContainer(
                                showIndicator: false,
                                backgroundColor: Colors.transparent,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image(
                                      image: R.image.rtc_live(),
                                      width: 50.r,
                                      height: 50.r,
                                    ),
                                    Text(
                                      " ${status.doctors.length}",
                                      style: TextStyle(
                                        fontSize: 48.sp,
                                        fontWeight: FontWeight.w500,
                                        height: 1,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // 直播在线人数
                          Visibility(
                            visible:
                                networkConnected &&
                                status.liveUserCount != null,
                            child: Padding(
                              padding: EdgeInsets.only(right: 18.w),
                              child: CornerContainer(
                                showIndicator: false,
                                backgroundColor: Colors.transparent,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image(
                                      image: R.image.rtm_live(),
                                      width: 50.r,
                                      height: 50.r,
                                    ),
                                    Text(
                                      " ${status.liveUserCount}",
                                      style: TextStyle(
                                        fontSize: 48.sp,
                                        fontWeight: FontWeight.w500,
                                        height: 1,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // RTC
                          if (status.hasSignal)
                            Padding(
                              padding: EdgeInsets.only(right: 18.w),
                              child: CornerContainer(
                                backgroundColor: Colors.transparent,
                                showIndicator: false,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                          width: 23.r,
                                          height: 23.r,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFD4353F),
                                            borderRadius: BorderRadius.circular(
                                              12.5.r,
                                            ),
                                          ),
                                        )
                                        .animate(
                                          onComplete: (controller) {
                                            controller.repeat(reverse: true);
                                          },
                                        )
                                        .fade(duration: 500.ms),
                                    SizedBox(
                                      height: 50.h,
                                      child: Text(
                                        " REC",
                                        style: TextStyle(
                                          fontSize: 45.sp,
                                          fontWeight: FontWeight.w500,
                                          height: 1,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          // 网络
                          CornerContainer(
                            backgroundColor: Colors.transparent,
                            showIndicator: false,
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  AppContext.share
                                      .getNetWorkStatusIcon(
                                        networkType: status.networkType,
                                      )
                                      .keyName,
                                  width: 50.r,
                                  height: 50.r,
                                ),
                                Visibility(
                                  visible: networkConnected,
                                  child: Text.rich(
                                    TextSpan(
                                      text: " ${status.videoBitrate}",
                                      style: TextStyle(
                                        fontSize: 35.sp,
                                        fontWeight: FontWeight.w500,
                                        height: 1,
                                      ),
                                      children: [
                                        TextSpan(
                                          text: "kb/s",
                                          style: TextStyle(
                                            fontSize: 25.sp,
                                            fontWeight: FontWeight.w500,
                                            height: 1,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                if (!status.hasSignal) noSignalDialog(),
              ],
            )
            : Image.asset(R.image.no_picture().assetName, fit: BoxFit.fitWidth);
      },
    );
  }

  Widget noSignalDialog() {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.6),
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Container(
            width: 550,
            height: 284,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.all(Radius.circular(12)),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.5),
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFD4353F),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.white,
                        size: 40,
                      ),
                      SizedBox(width: 12),
                      Text(
                        '未收到腔镜信号',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      '请确认腔镜正常开启\n且视频连接线两端插稳',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.w400,
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
