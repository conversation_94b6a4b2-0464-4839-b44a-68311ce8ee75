import 'dart:async';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/material.dart';
import 'package:surgsmart/src/models/isar/surgery_record.model.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';
import 'package:surgsmart/src/models/screen_status.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/tools/drawing_board_notifier.dart';

/// 所属模块: expand_screen
///
/// 扩展屏幕显示
class ExpandScreenController extends AppController with StateMixin<ApiModel> {
  ExpandScreenController(super.key, super.routerState);

  static final String statusInfoTag = 'statusInfo';
  static final String videoFrameInfoTag = 'videoFrameInfo'; //自定义视频帧信息
  static final String drawingBoardNotifierTag = 'drawingBoardNotifier';

  Monitor? monitorController;

  ///扩展的第二路视频源控制器
  Monitor? monitor2Controller;

  /// 白板绘图
  final graphics = <UserGraphic>[].notifier;

  final statusInfo = ExpandScreenStatusInfo().initWith({}).notifier;

  OfflineSurgeryData? surgeryData;
  final render = ValueNotifier<ffi.TextureRendererRef?>(null);

  @override
  void onInit() {
    HostDevice.share.videoSources.addListener(createLocalMediaHandler);
    HostDevice.share.startListenVideoCaptureDevices();

    ///窗口消息监听
    setMultiWindowMethodHandler();

    renderInit();
  }

  @override
  void onReady() async {
    //await readSurgeryData();
    update(LoadState.success(ApiModel()));
  }

  @override
  void onClose() {
    graphics.dispose();
    statusInfo.dispose();
    //videoFrameInfo.dispose();
    if (render.value != null) {
      ffi.textureRendererDestroy(render.value!);
      render.value = null;
    }
  }

  /// 创建本地媒体处理器
  Future<void> createLocalMediaHandler() async {
    if (monitorController != null || getVideoCapture() == null) return;
    if (Platform.isLinux) {
      monitorController = Monitor(
        videoCaptureDevice: getVideoCapture()!,
        framerate: 30,
      );

      if (!monitorController!.init()) {
        throw "监视控制器初始化失败!";
      }
      update(LoadState.success(ApiModel()));
    }
  }

  ffi.VideoCaptureDevice? getVideoCapture({String? path}) {
    app.logW(
      'VideoCaptureDevice size:  ${HostDevice.share.videoSources.value.length}',
    );
    for (var video in HostDevice.share.videoSources.value) {
      if (path == null) {
        if (!video.isUsbExtend) return video;
      } else if (video.path.dartString == path) {
        return video;
      }
    }
    return null;
  }

  refreshHandler() => graphics.value = [...graphics.value];

  /// 互动消息处理
  void interactionMessageHandler(Map<String, dynamic> map) {
    String? type = map['type'];
    if (type == null) return;
    if (type == 'drawCursor') {
      Graphic message = Graphic().initWith(map);
      DrawingBoardNotifier.drawCursor(
        getOrCreateGraphic(message.userId),
        message,
      );
    } else if (type == 'clearGraphics') {
      GraphicSet message = GraphicSet().initWith(map);
      DrawingBoardNotifier.clearGraphics(
        getOrCreateGraphic(message.userId),
        uuids: message.uuids,
      );
    } else if (type == 'drawGraphic') {
      Graphic message = Graphic().initWith(map);
      DrawingBoardNotifier.drawGraphic(
        getOrCreateGraphic(message.userId),
        message,
        message,
      );
    } else if (type == 'redo') {
      GraphicSet message = GraphicSet().initWith(map);
      DrawingBoardNotifier.redo(
        getOrCreateGraphic(message.userId),
        message.uuids,
      );
    } else if (type == 'undo') {
      GraphicSet message = GraphicSet().initWith(map);
      DrawingBoardNotifier.undo(
        getOrCreateGraphic(message.userId),
        message.uuids,
      );
    } else if (type == 'onDoing') {
      Graphic message = Graphic().initWith(map);
      DrawingBoardNotifier.drawGraphic(getOrCreateGraphic(0), message, null);
    }

    refreshHandler();
  }

  UserGraphic getOrCreateGraphic(int userId) {
    var userGraphic =
        graphics.value.where((g) => g.userId == userId).firstOrNull;
    final doctor =
        statusInfo.value.doctors.where((d) => d.id == userId).firstOrNull;

    if (userGraphic == null) {
      userGraphic = UserGraphic().copyWith(
        userId: userId,
        name: doctor?.name ?? '-',
      );
      graphics.value.add(userGraphic);
    } else {
      userGraphic.userId = userId;
      userGraphic.name = doctor?.name ?? '-';
    }
    return userGraphic;
  }

  void setMultiWindowMethodHandler() {
    DesktopMultiWindow.setMethodHandler((call, fromWindowId) async {
      //debugPrint('${call.method} ${call.arguments} $fromWindowId');
      // if (surgeryData == null) {
      //   readSurgeryData();
      // }
      // 处理来自主窗口的消息
      if (call.method == statusInfoTag && call.arguments is Map) {
        ExpandScreenStatusInfo status = ExpandScreenStatusInfo().initWith(
          call.arguments.cast<String, dynamic>(),
        );
        checkExternalVideo(status.externalVideoDevice);
        refreshDrawingBoard();

        statusInfo.value = status;
      } else if (call.method == drawingBoardNotifierTag &&
          call.arguments is Map) {
        interactionMessageHandler(call.arguments.cast<String, dynamic>());
      } else if (call.method == videoFrameInfoTag) {
        //videoFrameFresh(call.arguments);
        return renderInit();
      }
      return true;
    });
  }

  // Future<void> readSurgeryData() async {
  //   surgeryData ??= await DbUtils.instance.queryRecentSurgeryRecord();
  //   app.logW('手术记录: ${surgeryData.toString()}');
  // }

  void checkExternalVideo(String? externalVideoDevice) async {
    if (externalVideoDevice != null) {
      if (monitor2Controller == null) {
        final videoCaptureDevice = getVideoCapture(path: externalVideoDevice);
        if (videoCaptureDevice == null) return;
        monitor2Controller = Monitor(
          videoCaptureDevice: videoCaptureDevice,
          width: 1920,
          height: 1080,
          framerate: 30,
        );
        if (monitor2Controller?.init() != true) {
          throw "监视控制器初始化失败!";
        }
      }
    } else {
      monitor2Controller?.dispose();
      monitor2Controller = null;
    }
  }

  void refreshDrawingBoard() {
    for (var g in graphics.value) {
      DrawingBoardNotifier.removeGraphic(g, DateTime.now());
    }
    refreshHandler();
  }

  int renderInit() {
    render.value ??= ffi.textureRendererCreate(
      MediasKit.pluginId.toNativeCharPointer(this),
    );
    return render.value!.address;
  }
}
